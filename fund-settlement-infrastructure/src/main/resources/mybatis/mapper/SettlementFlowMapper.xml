<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shouqianba.trade.fund.settlement.infrastructure.adapter.datasource.mysql.settlementflow.SettlementFlowDao">

    <resultMap type="com.shouqianba.trade.fund.settlement.infrastructure.adapter.datasource.mysql.settlementflow.po.SettlementFlowPO" id="SettlementFlowMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="transSn" column="trans_sn" jdbcType="VARCHAR"/>
        <result property="orderSn" column="order_sn" jdbcType="VARCHAR"/>
        <result property="batchId" column="batch_id" jdbcType="BIGINT"/>
        <result property="poolId" column="pool_id" jdbcType="BIGINT"/>
        <result property="fromType" column="from_type" jdbcType="INTEGER"/>
        <result property="fromBrandSn" column="from_brand_sn" jdbcType="VARCHAR"/>
        <result property="fromMerchantSn" column="from_merchant_sn" jdbcType="VARCHAR"/>
        <result property="fromStoreSn" column="from_store_sn" jdbcType="VARCHAR"/>
        <result property="fromInfo" column="from_info" typeHandler="com.shouqianba.trade.fund.settlement.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="toType" column="to_type" jdbcType="INTEGER"/>
        <result property="toBrandSn" column="to_brand_sn" jdbcType="VARCHAR"/>
        <result property="toMerchantSn" column="to_merchant_sn" jdbcType="VARCHAR"/>
        <result property="toStoreSn" column="to_store_sn" jdbcType="VARCHAR"/>
        <result property="toInfo" column="to_info" typeHandler="com.shouqianba.trade.fund.settlement.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="amount" column="amount" typeHandler="com.shouqianba.trade.fund.settlement.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="tradeInfo" column="trade_info" typeHandler="com.shouqianba.trade.fund.settlement.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="bizInfo" column="biz_info" typeHandler="com.shouqianba.trade.fund.settlement.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="ctime" column="ctime" typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="mtime" column="mtime" typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="table">
        settlement_flow
    </sql>

    <sql id="allColumn">
        id,
        type,
        trans_sn,
        order_sn,
        batch_id,
        pool_id,
        from_type,
        from_brand_sn,
        from_merchant_sn,
        from_store_sn,
        from_info,
        to_type,
        to_brand_sn,
        to_merchant_sn,
        to_store_sn,
        to_info,
        amount,
        trade_info,
        biz_info,
        status,
        ctime,
        mtime,
        version
    </sql>

    <sql id="insertColumnValue">
        #{id},
        #{type},
        #{transSn},
        #{orderSn},
        #{batchId},
        #{poolId},
        #{fromType},
        #{fromBrandSn},
        #{fromMerchantSn},
        #{fromStoreSn},
        #{fromInfo},
        #{toType},
        #{toBrandSn},
        #{toMerchantSn},
        #{toStoreSn},
        #{toInfo},
        #{amount},
        #{tradeInfo},
        #{bizInfo},
        #{status},
        #{ctime},
        #{mtime},
        #{version}
    </sql>

    <insert id="insert">
        insert into
        <include refid="table"/>
        (
            <include refid="allColumn"/>
        )
        values
        (
            <include refid="insertColumnValue"/>
        )
    </insert>

    <insert id="batchInsert">
        insert into
        <include refid="table"/>
        (
        <include refid="allColumn"/>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.type}, #{item.transSn}, #{item.orderSn}, #{item.batchId},#{item.poolId},
            #{item.fromType}, #{item.fromBrandSn}, #{item.fromMerchantSn}, #{item.fromStoreSn},
            #{item.fromInfo},
            #{item.toType}, #{item.toBrandSn}, #{item.toMerchantSn}, #{item.toStoreSn},
            #{item.toInfo},
            #{item.amount}, #{item.tradeInfo}, #{item.bizInfo}, #{item.status},
            #{item.ctime}, #{item.mtime}, #{item.version}
            )
        </foreach>
    </insert>

    <update id="update">
        update
        <include refid="table"/>
        <set>
            <!-- 修改所有涉及到这些字段的条件判断 -->
            <if test="type != null">
                type = #{type},
            </if>
            <if test="transSn != null">
                trans_sn = #{transSn},
            </if>
            <if test="orderSn != null">
                order_sn = #{orderSn},
            </if>

            <if test="batchId != null">
                batch_id = #{batchId},
            </if>
            <if test="poolId != null">
                    pool_id = #{poolId},
            </if>
            <if test="fromType != null">
                from_type = #{fromType},
            </if>
            <if test="fromBrandSn != null">
                from_brand_sn = #{fromBrandSn},
            </if>
            <if test="fromMerchantSn != null">
                from_merchant_sn = #{fromMerchantSn},
            </if>
            <if test="fromStoreSn != null">
                from_store_sn = #{fromStoreSn},
            </if>
            <if test="fromInfo != null">
                from_info = #{fromInfo},
            </if>
            <if test="toType != null">
                to_type = #{toType},
            </if>
            <if test="toBrandSn != null">
                to_brand_sn = #{toBrandSn},
            </if>
            <if test="toMerchantSn != null">
                to_merchant_sn = #{toMerchantSn},
            </if>
            <if test="toStoreSn != null">
                to_store_sn = #{toStoreSn},
            </if>
            <if test="toInfo != null">
                to_info = #{toInfo},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="tradeInfo != null">
                trade_info = #{tradeInfo},
            </if>
            <if test="bizInfo != null">
                biz_info = #{bizInfo},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            mtime = #{mtime},
            version = version + 1
        </set>
        where id = #{id} and version = #{version}
    </update>

    <select id="select" resultMap="SettlementFlowMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="type != null">
                and type = #{type},
            </if>
            <if test="transSn != null">
                and trans_sn = #{transSn},
            </if>
            <if test="orderSn != null">
                and order_sn = #{orderSn},
            </if>
            <if test="batchId != null and batchId != ''">
                and batch_id = #{batchId}
            </if>
            <if test="poolId != null and poolId != ''">
                    and pool_id = #{poolId}
            </if>
            <if test="fromType != null">
                and from_type = #{fromType}
            </if>
            <if test="fromBrandSn != null and fromBrandSn != ''">
                and from_brand_sn = #{fromBrandSn}
            </if>
            <if test="fromMerchantSn != null and fromMerchantSn != ''">
                and from_merchant_sn = #{fromMerchantSn}
            </if>
            <if test="fromStoreSn != null and fromStoreSn != ''">
                and from_store_sn = #{fromStoreSn}
            </if>
            <if test="toType != null">
                and to_type = #{toType}
            </if>
            <if test="toBrandSn != null and toBrandSn != ''">
                and to_brand_sn = #{toBrandSn}
            </if>
            <if test="toMerchantSn != null and toMerchantSn != ''">
                and to_merchant_sn = #{toMerchantSn}
            </if>
            <if test="toStoreSn != null and toStoreSn != ''">
                and to_store_sn = #{toStoreSn}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
        </where>
        limit 1
    </select>


    <select id="selectForUpdate" resultMap="SettlementFlowMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        where id = #{id} for update
    </select>

    <select id="selectForUpdateSkipLocked" resultMap="SettlementFlowMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        where id = #{id} for update skip locked
    </select>

    <select id="batchSelect" resultMap="SettlementFlowMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <if test="id != null">
                id = #{id}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="transSn != null">
                and trans_sn = #{transSn}
            </if>
            <if test="orderSn != null">
                and order_sn = #{orderSn}
            </if>
            <if test="batchId != null and batchId != ''">
                and batch_id = #{batchId}
            </if>
            <if test="poolId != null and poolId != ''">
                and pool_id = #{poolId}
            </if>
            <if test="fromType != null">
                and from_type = #{fromType}
            </if>
            <if test="fromBrandSn != null and fromBrandSn != ''">
                and from_brand_sn = #{fromBrandSn}
            </if>
            <if test="fromMerchantSn != null and fromMerchantSn != ''">
                and from_merchant_sn = #{fromMerchantSn}
            </if>
            <if test="fromStoreSn != null and fromStoreSn != ''">
                and from_store_sn = #{fromStoreSn}
            </if>
            <if test="toType != null">
                and to_type = #{toType}
            </if>
            <if test="toBrandSn != null and toBrandSn != ''">
                and to_brand_sn = #{toBrandSn}
            </if>
            <if test="toMerchantSn != null and toMerchantSn != ''">
                and to_merchant_sn = #{toMerchantSn}
            </if>
            <if test="toStoreSn != null and toStoreSn != ''">
                and to_store_sn = #{toStoreSn}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="cursorField != null and endCursor != null">
                <if test="isDesc == false">
                    and `${cursorField}` <![CDATA[>]]> #{endCursor}
                </if>
                <if test="isDesc == true">
                    and `${cursorField}` <![CDATA[<]]> #{endCursor}
                </if>
            </if>
        </where>
        <if test="sortField != null">
            order by `${sortField}`
            <if test="isDesc == false">
                asc
            </if>
            <if test="isDesc == true">
                desc
            </if>
        </if>
        <choose>
            <!--偏移量分页-->
            <when test="offset != null and querySize != null">
                limit ${offset}, ${querySize}
            </when>
            <when test="offset == null and querySize != null">
                limit ${querySize}
            </when>
            <otherwise>
                limit 1000
            </otherwise>
        </choose>
    </select>

</mapper>