#servlet容器配置
server.tomcat.threads.max=500
server.tomcat.threads.min-spare=100
server.tomcat.accept-count=500
server.tomcat.connection-timeout=10s
#数据源配置
spring.datasource.type=com.zaxxer.hikari.util.DriverDataSource
#核心数据源
spring.datasource.core.hikari.pool-name=core-pool
spring.datasource.core.hikari.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.core.hikari.jdbc-url=pk-fund-settlement-fund_settlement-6410?serverTimezone=Asia/Shanghai&socketTimeout=300000
spring.datasource.core.hikari.minimum-idle=20
spring.datasource.core.hikari.idle-timeout=300000
spring.datasource.core.hikari.maximum-pool-size=100
spring.datasource.core.hikari.connection-timeout=5000
spring.datasource.core.hikari.connection-test-query=select 1
spring.datasource.core.hikari.connection-init-sql=set names utf8mb4
#边缘数据源
spring.datasource.periphery.hikari.pool-name=periphery-pool
spring.datasource.periphery.hikari.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.periphery.hikari.jdbc-url=pk-fund-settlement-fund_settlement-6410?serverTimezone=Asia/Shanghai&socketTimeout=300000
spring.datasource.periphery.hikari.minimum-idle=50
spring.datasource.periphery.hikari.idle-timeout=300000
spring.datasource.periphery.hikari.maximum-pool-size=200
spring.datasource.periphery.hikari.connection-timeout=5000
spring.datasource.periphery.hikari.connection-test-query=select 1
spring.datasource.periphery.hikari.connection-init-sql=set names utf8mb4
#交易订单业务事件kafka生产者&消费者配置
spring.kafka.bootstrap-servers=aliyun-kafka-01.shouqianba.com:9092,aliyun-kafka-02.shouqianba.com:9092,aliyun-kafka-03.shouqianba.com:9092
spring.kafka.listener.type=batch
spring.kafka.producer.batch-size=16384
spring.kafka.producer.acks=1
spring.kafka.producer.retries=3
spring.kafka.producer.properties.linger.ms=50
spring.kafka.producer.properties.max.block.ms=5000
spring.kafka.producer.properties.enable.idempotence=false
spring.kafka.consumer.max-poll-records=1000
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.group-id=fund-settlement
spring.kafka.consumer.key-deserializer=org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
spring.kafka.consumer.value-deserializer=org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
spring.kafka.consumer.properties.spring.deserializer.key.delegate.class=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.properties.spring.deserializer.value.delegate.class=org.apache.kafka.common.serialization.StringDeserializer
#品牌中心
spring.kafka.consumer.channel.brand.topic.brand-business=events_CUA_base-brand-business
spring.kafka.consumer.channel.brand.group-id=fund-settlement-consumer
spring.kafka.consumer.channel.brand.bootstrap-servers=aliyun-kafka-01.shouqianba.com:9092,aliyun-kafka-02.shouqianba.com:9092,aliyun-kafka-03.shouqianba.com:9092
spring.kafka.consumer.channel.brand.max-poll-records=20
spring.kafka.consumer.channel.brand.enable-auto-commit=true
spring.kafka.consumer.channel.brand.schema-registry-url=http://aliyun-schema-01.shouqianba.com:8081,http://aliyun-schema-02.shouqianba.com:8081,http://aliyun-schema-03.shouqianba.com:8081
#清分业务事件
spring.kafka.producer.topic.fund-settlement=events_TRADE_fund-settlement-event
spring.kafka.consumer.topic.fund-settlement=events_TRADE_fund-settlement-event
#商户中心
service.rpc.brand-business=http://brand-business
#资金核心
service.rpc.fund-core=http://fund-core
#抖音token获取
service.rpc.scorpio=http://scorpio
#core-business
service.rpc.core-business=http://app-core-business
#调整交易
service.rpc.trade-manage=http://trade-manage-service
#brand-settle
service.rpc.brand-settle=http://brand-settle