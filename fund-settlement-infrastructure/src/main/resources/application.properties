spring.application.name=fund-settlement
spring.mvc.servlet.load-on-startup=1
server.tomcat.mbeanregistry.enabled=true
app.id=fund-settlement
app.name=fund-settlement
app.group=trade
#servlet容器配置
server.tomcat.threads.max=500
server.tomcat.threads.min-spare=100
server.tomcat.accept-count=500
server.tomcat.connection-timeout=10s
mybatis.config-location=classpath:mybatis/mybatis-config.xml
mybatis.mapper-locations=classpath:mybatis/**/*Mapper.xml
#kafka消费者开关
kafka.consumer.enable=true
#kafka消息堆积监控开关
kafka.monitor.enable=true
#飞书通知配置
feishu.notify.url=https://open.feishu.cn/open-apis/bot/v2/hook/7d92ecc1-63bf-4783-b805-c350deb261e8
feishu.notify.userids=014939,015356
#火山appid
volcano.app.id=10000038
#火山用户appid
volcano.user.app.id=10000003
#火山数据同步topic
spring.kafka.producer.topic.volcano=analytics_data_volcengine_push
#ok-http的边缘请求配置
ok.http.periphery.core-pool-size=10
ok.http.periphery.max-pool-size=50
ok.http.periphery.call-timeout=2500
#ok-http的核心请求配置
ok.http.core.core-pool-size=50
ok.http.core.max-pool-size=100
ok.http.core.call-timeout=6500
#CSV导出配置
fund.settlement.csv.export.batch-size=1000
fund.settlement.csv.export.output-dir=/tmp/settlement/csv
fund.settlement.csv.export.file-name-prefix=settlement_flow
fund.settlement.csv.export.enable-compression=true
fund.settlement.csv.export.max-memory-usage-mb=100
fund.settlement.csv.export.write-timeout-seconds=300

#阿里云oss身份标识
aliyun.oss.endpoint=http://oss-cn-hangzhou.aliyuncs.com
aliyun.oss.group-name=trade
aliyun.oss.bucket-name=sqb-core
