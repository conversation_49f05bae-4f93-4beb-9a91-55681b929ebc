package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.oss.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> Date: 2025/6/17 Time: 16:20 PM
 */
@Getter
@SuperBuilder(toBuilder = true)
public class OssUploadRequest extends BaseOssRequest {

    @JsonIgnore
    private byte[] content;

    private String fileName;

}
