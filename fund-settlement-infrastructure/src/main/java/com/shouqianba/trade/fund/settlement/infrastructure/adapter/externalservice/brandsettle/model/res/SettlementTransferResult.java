package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res;

import com.wosai.upay.brandsettle.response.TransferResponse;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;

import java.util.Objects;

/**
 * 品牌结算转账结果
 */
@Getter
@Builder(access = AccessLevel.PROTECTED)
public class SettlementTransferResult {
    public static final SettlementTransferResult DEFAULT = SettlementTransferResult
            .builder()
            .build();

    /**
     * 转账sn
     */
    private final String sn;

    /**
     * 转账客户端流水号
     */
    private final String clientSn;

    /**
     * 转账状态
     * 0: 转账待执行, 1: 转账成功, 2: 转账失败, 3: 转账处理中,
     * 4: 执行异常，5:冻结成功，6:解冻，7:冻结回退
     */
    private final Integer status;

    /**
     * 转账金额
     */
    private final Long amount;

    /**
     * 转账错误描述
     */
    private final String errorMsg;

    /**
     * 创建TransferResult实例
     *
     * @param response 响应
     * @return TransferResult实例
     */
    public static SettlementTransferResult newInstance(TransferResponse response) {
        if (Objects.isNull(response)) {
            return DEFAULT;
        }

        return SettlementTransferResult.builder()
                .sn(response.getSn())
                .clientSn(response.getClientSn())
                .status(response.getStatus())
                .amount(response.getAmount())
                .errorMsg(response.getErrorMsg())
                .build();
    }

    public boolean isExists() {
        return Objects.nonNull(sn);
    }

}