package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.upay.brandsettle.response.AccountWalletResponse;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;

import java.util.Objects;

/**
 * 品牌结算账户余额结果
 *
 * <AUTHOR> Date: 2025/7/15 Time: 10:20 AM
 */
@Getter
@Builder(access = AccessLevel.PROTECTED)
public class SettlementAccountWalletResult {
    public static final SettlementAccountWalletResult DEFAULT = SettlementAccountWalletResult.builder().build();

    /**
     * 外部商户编号
     */
    @JsonProperty("out_merchant_no")
    private String outMerchantNo;

    /**
     * 商户编号
     */
    @JsonProperty("merchant_sn")
    private String merchantSn;

    /**
     * 商户名称
     */
    @JsonProperty("merchant_name")
    private String merchantName;

    /**
     * 账户号
     */
    @JsonProperty("account_no")
    private String accountNo;

    /**
     * 会员ID
     */
    @JsonProperty("member_id")
    private String memberId;

    /**
     * 余额（可用余额 + 在途余额）
     */
    private Long balance;

    /**
     * 可用余额/显示的总余额
     */
    @JsonProperty("available_balance")
    private Long availableBalance;

    /**
     * 待结算余额
     */
    @JsonProperty("settlement_balance")
    private Long settlementBalance;

    /**
     * 可提现余额
     */
    @JsonProperty("withdraw_balance")
    private Long withdrawBalance;

    /**
     * 冻结余额
     */
    @JsonProperty("froze_balance")
    private Long frozeBalance;

    /**
     * 预留金额
     */
    @JsonProperty("reserved_amount")
    private Long reservedAmount;

    /**
     * 充值余额
     */
    @JsonProperty("recharge_balance")
    private Long rechargeBalance;

    /**
     * 银行账户名称
     */
    @JsonProperty("bank_account_name")
    private String bankAccountName;

    /**
     * 待转账余额
     */
    @JsonProperty("waiting_carry_balance")
    private Long waitingCarryBalance;

    /**
     * 创建AccountWalletResult实例
     *
     * @param response 响应
     * @return AccountWalletResult实例
     */
    public static SettlementAccountWalletResult newInstance(AccountWalletResponse response) {
        if (response == null) {
            return DEFAULT;
        }

        return SettlementAccountWalletResult.builder()
                .outMerchantNo(response.getOutMerchantNo())
                .merchantSn(response.getMerchantSn())
                .merchantName(response.getMerchantName())
                .accountNo(response.getAccountNo())
                .memberId(response.getMemberId())
                .balance(response.getBalance())
                .availableBalance(response.getAvailableBalance())
                .settlementBalance(response.getSettlementBalance())
                .withdrawBalance(response.getWithdrawBalance())
                .frozeBalance(response.getFrozeBalance())
                .reservedAmount(response.getReservedAmount())
                .rechargeBalance(response.getRechargeBalance())
                .bankAccountName(response.getBankAccountName())
                .waitingCarryBalance(response.getWaitingCarryBalance())
                .build();
    }

    /**
     * 是否存在
     *
     * @return 是否存在
     */
    public boolean isExist() {
        return Objects.nonNull(accountNo);
    }
}