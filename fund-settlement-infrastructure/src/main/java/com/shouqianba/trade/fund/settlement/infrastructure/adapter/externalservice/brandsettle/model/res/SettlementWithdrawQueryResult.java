package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res;

import com.wosai.upay.brandsettle.response.WithdrawQueryResponse;
import lombok.Builder;
import lombok.Getter;

/**
 * 品牌结算提现查询结果
 *
 * <AUTHOR> Date: 2025/6/15 Time: 10:20 AM
 */
@Getter
@Builder
public class SettlementWithdrawQueryResult {
    public static final SettlementWithdrawQueryResult DEFAULT = SettlementWithdrawQueryResult.builder().build();

    /**
     * 提现任务id
     */
    private String id;

    /**
     * 品牌id
     */
    private String brandId;

    /**
     * 商户id
     */
    private String merchantId;

    /**
     * 提现外部单号
     */
    private String clientSn;

    /**
     * 提现状态
     */
    private Integer status;

    /**
     * 提现单号
     */
    private String tradeNo;

    /**
     * 提现处理完成时间
     */
    private Long finishTime;

    /**
     * 提现金额
     */
    private Long amount;

    /**
     * 提现手续费
     */
    private Long fee;

    /**
     * 提现银行卡卡号
     */
    private String cardNo;

    /**
     * 提现银行卡名称
     */
    private String cardBankName;

    /**
     * 提现银行卡图标
     */
    private String cardBankIcon;

    /**
     * 提现时间
     */
    private Long ctime;

    /**
     * 创建WithdrawQueryResult实例
     *
     * @param response 响应
     * @return WithdrawQueryResult实例
     */
    public static SettlementWithdrawQueryResult newInstance(WithdrawQueryResponse response) {
        if (response == null) {
            return DEFAULT;
        }

        return SettlementWithdrawQueryResult.builder()
                .status(response.getStatus())
                .amount(response.getAmount())
                .merchantId(response.getMerchantId())
                .id(response.getId())
                .brandId(response.getBrandId())
                .clientSn(response.getClientSn())
                .tradeNo(response.getTradeNo())
                .finishTime(response.getFinishTime())
                .fee(response.getFee())
                .cardNo(response.getCardNo())
                .cardBankName(response.getCardBankName())
                .cardBankIcon(response.getCardBankIcon())
                .ctime(response.getCtime())
                .build();
    }
}