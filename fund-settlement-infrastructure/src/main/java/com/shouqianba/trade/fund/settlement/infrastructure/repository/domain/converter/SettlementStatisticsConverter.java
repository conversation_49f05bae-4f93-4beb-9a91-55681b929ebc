package com.shouqianba.trade.fund.settlement.infrastructure.repository.domain.converter;

import com.shouqianba.trade.fund.settlement.common.exception.FundSettlementBizException;
import com.shouqianba.trade.fund.settlement.common.exception.enums.FundSettlementRespCodeEnum;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.datasource.mysql.settlementstatistics.po.SettlementStatisticsPO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.AccountTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.SettlementStatisticsAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.SettlementStatisticsAggrRootFactory;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.enums.StatisticsTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo.SettlementStatisticsAmountVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo.SettlementStatisticsBizDomainVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo.SettlementStatisticsExtVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo.SettlementStatisticsPayeeInfoVO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 结算统计转换器
 *
 * <AUTHOR> Date: 2025/07/15 Time: 10:34 AM
 */
@Component
public class SettlementStatisticsConverter {

    /**
     * 聚合根转PO
     *
     * @param aggrRoot 聚合根
     * @return PO
     */
    public SettlementStatisticsPO toSettlementStatisticsPO(SettlementStatisticsAggrRoot aggrRoot) {
        if (Objects.isNull(aggrRoot) || aggrRoot.isNotExist()) {
            throw new FundSettlementBizException(FundSettlementRespCodeEnum.SETTLEMENT_STATISTICS_NOT_EXIST);
        }
        return new SettlementStatisticsPO()
                .setId(aggrRoot.getId())
                .setType(aggrRoot.getType().getCode())
                .setPayeeType(aggrRoot.getPayeeType().getCode())
                .setBrandSn(aggrRoot.getBrandSn())
                .setMerchantSn(aggrRoot.getMerchantSn())
                .setStoreSn(aggrRoot.getStoreSn())
                .setPayeeInfo(aggrRoot.getPayeeInfo().toJsonNode())
                .setAcquiringCompany(aggrRoot.getAcquiringCompany())
                .setStatisticsDate(aggrRoot.getStatisticsDate())
                .setAmount(aggrRoot.getAmount().toJsonNode())
                .setBizDomain(Objects.nonNull(aggrRoot.getBizDomain()) ? aggrRoot.getBizDomain().toJsonNode() : null)
                .setExt(Objects.nonNull(aggrRoot.getExt()) ? aggrRoot.getExt().toJsonNode() : null)
                .setCtime(aggrRoot.getCreated())
                .setMtime(aggrRoot.getUpdated())
                .setVersion(aggrRoot.getVersion());
    }

    /**
     * 聚合根列表转PO列表
     *
     * @param aggrRoots 聚合根列表
     * @return PO列表
     */
    public List<SettlementStatisticsPO> toSettlementStatisticsPOList(List<SettlementStatisticsAggrRoot> aggrRoots) {
        if (Objects.isNull(aggrRoots) || aggrRoots.isEmpty()) {
            return Collections.emptyList();
        }
        return aggrRoots
                .stream()
                .map(this::toSettlementStatisticsPO)
                .collect(Collectors.toList());
    }

    /**
     * PO转聚合根
     *
     * @param po PO
     * @return 聚合根
     */
    public SettlementStatisticsAggrRoot toSettlementStatisticsAggrRoot(SettlementStatisticsPO po) {
        if (Objects.isNull(po) || Objects.isNull(po.getId())) {
            return SettlementStatisticsAggrRoot.newEmptyInstance();
        }
        try {
            return SettlementStatisticsAggrRootFactory
                    .builder()
                    .coreBuilder()
                    .id(po.getId())
                    .type(StatisticsTypeEnum.ofCode(po.getType()))
                    .payeeType(AccountTypeEnum.ofCode(po.getPayeeType()))
                    .brandSn(po.getBrandSn())
                    .merchantSn(po.getMerchantSn())
                    .storeSn(po.getStoreSn())
                    .payeeInfo(SettlementStatisticsPayeeInfoVO.genFromJsonObject(po.getPayeeInfo(),
                            SettlementStatisticsPayeeInfoVO.class))
                    .acquiringCompany(po.getAcquiringCompany())
                    .statisticsDate(po.getStatisticsDate())
                    .amount(SettlementStatisticsAmountVO.genFromJsonObject(po.getAmount(),
                            SettlementStatisticsAmountVO.class))
                    .optionalBuilder()
                    .bizDomain(SettlementStatisticsBizDomainVO.genFromJsonObject(po.getBizDomain(),
                            SettlementStatisticsBizDomainVO.class))
                    .ext(SettlementStatisticsExtVO.genFromJsonObject(po.getExt(),
                            SettlementStatisticsExtVO.class))
                    .created(po.getCtime())
                    .updated(po.getMtime())
                    .version(po.getVersion())
                    .build();
        } catch (Exception ignore) {
            return SettlementStatisticsAggrRoot.newEmptyInstance();
        }
    }

    /**
     * PO列表转聚合根列表
     *
     * @param pos PO列表
     * @return 聚合根列表
     */
    public List<SettlementStatisticsAggrRoot> toSettlementStatisticsAggrRootList(List<SettlementStatisticsPO> pos) {
        if (Objects.isNull(pos) || pos.isEmpty()) {
            return Collections.emptyList();
        }
        return pos
                .stream()
                .map(this::toSettlementStatisticsAggrRoot)
                .collect(Collectors.toList());
    }
}