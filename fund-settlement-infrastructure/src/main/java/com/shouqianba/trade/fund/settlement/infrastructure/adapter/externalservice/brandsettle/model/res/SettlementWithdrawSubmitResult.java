package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res;

import com.wosai.upay.brandsettle.response.WithdrawResponse;
import lombok.Builder;
import lombok.Getter;

import java.util.Objects;

/**
 * 品牌结算提现结果
 * <AUTHOR> Date: 2025/6/15 Time: 10:20 AM
 */
@Getter
@Builder
public class SettlementWithdrawSubmitResult {
    
    public static final SettlementWithdrawSubmitResult DEFAULT = SettlementWithdrawSubmitResult.builder().build();
    
    /**
     * 提现单号
     */
    private String withdrawSn;
    
    /**
     * 提现状态：1-处理中 2-成功 3-失败
     */
    private Integer status;
    
    /**
     * 提现金额（单位：分）
     */
    private Long amount;

    /**
     * 提现金额（单位：分）
     */
    private String clientSn;
    
    /**
     * 创建WithdrawSubmitResult实例
     *
     * @param response 响应
     * @return WithdrawSubmitResult实例
     */
    public static SettlementWithdrawSubmitResult newInstance(WithdrawResponse response) {
        if (Objects.isNull(response)) {
            return DEFAULT;
        }
        
        return SettlementWithdrawSubmitResult.builder()
                .withdrawSn(response.getSn())
                .status(response.getStatus())
                .amount(response.getAmount())
                .clientSn(response.getClientSn())
                .build();
    }
}