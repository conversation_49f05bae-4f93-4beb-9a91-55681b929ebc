package com.shouqianba.trade.fund.settlement.infrastructure.repository.domain.converter;

import com.shouqianba.trade.fund.settlement.common.exception.FundSettlementBizException;
import com.shouqianba.trade.fund.settlement.common.exception.enums.FundSettlementRespCodeEnum;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.datasource.mysql.settlementflow.po.SettlementFlowPO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.SettlementFlowAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.SettlementFlowAggrRootFactory;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.AccountTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.FlowTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.SettlementStatusEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowAccountInfoVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowAmountVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowBizInfoVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowTradeInfoVO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/***
 * <AUTHOR> Date: 2025/04/24 Time: 04:34 PM
 */
@Component
public class SettlementFlowAggrRootConverter {

    public SettlementFlowPO toSettlementFlowPO(SettlementFlowAggrRoot aggrRoot) {
        if (Objects.isNull(aggrRoot) || aggrRoot.isNotExist()) {
            throw new FundSettlementBizException(FundSettlementRespCodeEnum.SETTLEMENT_FLOW_NOT_EXIST);
        }
        return new SettlementFlowPO()
                .setId(aggrRoot.getId())
                .setType(aggrRoot
                        .getType()
                        .getCode())
                .setTransSn(aggrRoot.getTransSn())
                .setOrderSn(aggrRoot.getOrderSn())
                .setBatchId(aggrRoot.getBatchId())
                .setPoolId(aggrRoot.getPoolId())
                .setFromType(aggrRoot
                        .getFromType()
                        .getCode())
                .setFromBrandSn(aggrRoot.getFromBrandSn())
                .setFromMerchantSn(aggrRoot.getFromMerchantSn())
                .setFromStoreSn(aggrRoot.getFromStoreSn())
                .setFromInfo(aggrRoot
                        .getFromInfo()
                        .toJsonNode())
                .setToType(aggrRoot
                        .getToType()
                        .getCode())
                .setToBrandSn(aggrRoot.getToBrandSn())
                .setToMerchantSn(aggrRoot.getToMerchantSn())
                .setToStoreSn(aggrRoot.getToStoreSn())
                .setToInfo(aggrRoot
                        .getToInfo()
                        .toJsonNode())
                .setAmount(aggrRoot
                        .getAmount()
                        .toJsonNode())
                .setTradeInfo(aggrRoot
                        .getTradeInfo()
                        .toJsonNode())
                .setBizInfo(aggrRoot
                        .getBizInfo()
                        .toJsonNode())
                .setStatus(aggrRoot.getStatus().getCode())
                .setCtime(aggrRoot.getCreated())
                .setMtime(aggrRoot.getUpdated())
                .setVersion(aggrRoot.getVersion());
    }

    public List<SettlementFlowPO> toSettlementFlowPOList(List<SettlementFlowAggrRoot> aggrRoots) {
        if (Objects.isNull(aggrRoots) || aggrRoots.isEmpty()) {
            return Collections.emptyList();
        }
        return aggrRoots
                .stream()
                .map(this::toSettlementFlowPO)
                .collect(Collectors.toList());
    }

    public SettlementFlowAggrRoot toSettlementFlowAggrRoot(SettlementFlowPO po) {
        if (Objects.isNull(po) || Objects.isNull(po.getId())) {
            return SettlementFlowAggrRoot.newEmptyInstance();
        }
        try {
            return SettlementFlowAggrRootFactory
                    .builder()
                    .coreBuilder()
                    .id(po.getId())
                    .type(FlowTypeEnum.ofCode(po.getType()))
                    .transSn(po.getTransSn())
                    .orderSn(po.getOrderSn())
                    .batchId(po.getBatchId())
                    .poolId(po.getPoolId())
                    .fromType(AccountTypeEnum.ofCode(po.getFromType()))
                    .fromBrandSn(po.getFromBrandSn())
                    .fromMerchantSn(po.getFromMerchantSn())
                    .fromStoreSn(po.getFromStoreSn())
                    .fromInfo(SettlementFlowAccountInfoVO.genFromJsonObject(po.getFromInfo(),
                            SettlementFlowAccountInfoVO.class))
                    .toType(AccountTypeEnum.ofCode(po.getToType()))
                    .toBrandSn(po.getToBrandSn())
                    .toMerchantSn(po.getToMerchantSn())
                    .toStoreSn(po.getToStoreSn())
                    .toInfo(SettlementFlowAccountInfoVO.genFromJsonObject(po.getToInfo(),
                            SettlementFlowAccountInfoVO.class))
                    .amount(SettlementFlowAmountVO.genFromJsonObject(po.getAmount(),
                            SettlementFlowAmountVO.class))
                    .tradeInfo(SettlementFlowTradeInfoVO.genFromJsonObject(po.getTradeInfo(),
                            SettlementFlowTradeInfoVO.class))
                    .bizInfo(SettlementFlowBizInfoVO.genFromJsonObject(po.getBizInfo(),
                            SettlementFlowBizInfoVO.class))
                    .status(SettlementStatusEnum.ofCode(po.getStatus()))
                    .optionalBuilder()
                    .created(po.getCtime())
                    .updated(po.getMtime())
                    .version(po.getVersion())
                    .rebuild();
        } catch (Exception ignore) {
            return SettlementFlowAggrRoot.newEmptyInstance();
        }
    }

    public List<SettlementFlowAggrRoot> toSettlementFlowAggrRootList(List<SettlementFlowPO> pos) {
        if (Objects.isNull(pos) || pos.isEmpty()) {
            return Collections.emptyList();
        }
        return pos
                .stream()
                .map(this::toSettlementFlowAggrRoot)
                .collect(Collectors.toList());
    }
}
