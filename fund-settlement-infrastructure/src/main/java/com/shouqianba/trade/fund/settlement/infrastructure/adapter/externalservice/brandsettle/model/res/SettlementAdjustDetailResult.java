package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res;

import com.wosai.upay.brandsettle.enums.AdjustStatusEnum;
import com.wosai.upay.brandsettle.response.Adjust;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;

import java.util.Objects;

/**
 * 品牌结算调账详情结果
 */
@Getter
@Builder(access = AccessLevel.PROTECTED)
public class SettlementAdjustDetailResult {
    public static final SettlementAdjustDetailResult DEFAULT = SettlementAdjustDetailResult.builder().build();

    /**
     * 业务唯一ID
     */
    private final String id;

    /**
     * 品牌ID
     */
    private final String brandId;

    /**
     * 源商户SN码
     */
    private final String sourceMerchantSn;

    /**
     * 目标商户SN码
     */
    private final String targetMerchantSn;

    /**
     * 全局唯一流水号
     */
    private final String clientSn;

    /**
     * 转账状态码
     */
    private final Integer status;

    /**
     * 渠道完成时间（仅status=成功时有值）
     */
    private final Long channelFinishTime;

    /**
     * 金额（以分为单位的整数）
     */
    private final Long amount;

    /**
     * 创建SettlementAdjustDetailResult实例
     *
     * @param adjust 调账详情
     * @return SettlementAdjustDetailResult实例
     */
    public static SettlementAdjustDetailResult newInstance(Adjust adjust) {
        if (Objects.isNull(adjust)) {
            return DEFAULT;
        }

        return SettlementAdjustDetailResult.builder()
                .id(adjust.getId())
                .brandId(adjust.getBrandId())
                .sourceMerchantSn(adjust.getSourceMerchantSn())
                .targetMerchantSn(adjust.getTargetMerchantSn())
                .clientSn(adjust.getClientSn())
                .status(adjust.getStatus())
                .channelFinishTime(adjust.getChannelFinishTime())
                .amount(adjust.getAmount())
                .build();
    }

    public boolean isSuccess() {
        return Objects.equals(status, AdjustStatusEnum.SUCCESS.getCode());
    }

    public boolean isFailed() {
        return Objects.equals(status, AdjustStatusEnum.FAIL.getCode());
    }

    public boolean isProcessing() {
        return Objects.equals(status, AdjustStatusEnum.PROCESS.getCode());
    }


    public boolean isExists() {
        return Objects.nonNull(id);
    }
}