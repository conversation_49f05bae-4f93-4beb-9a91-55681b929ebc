package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.oss.model;

import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalUnit;
import java.util.Date;

/**
 * <AUTHOR> Date: 2025/6/17 Time: 16:20 PM
 */
@Data
@SuperBuilder(toBuilder = true)
public class BaseOssRequest {
    private static final long FILE_EXPIRE_DAY = 7L;

    private String bucketName;
    private String path;
    private Long expirationTime;


    public Date getExpirationTime(long offset, TemporalUnit temporalUnit) {
        if (offset <= 0) {
            return Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
        }
        return Date.from(LocalDateTime.now().plus(offset, temporalUnit).atZone(ZoneId.systemDefault()).toInstant());
    }

}
