package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res;

import com.wosai.upay.brandsettle.request.clearing.ClientClearingConfirmResponse;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;

import java.util.Objects;

/**
 * 品牌结算客户端
 *
 * <AUTHOR> Date: 2025/6/13 Time: 10:20 AM
 */
@Getter
@Builder(access = AccessLevel.PROTECTED)
public class SettlementClearingSubmitResult {
    public static final SettlementClearingSubmitResult DEFAULT = SettlementClearingSubmitResult
            .builder()
            .build();

    private final ClientClearingConfirmResponse response;

    public static SettlementClearingSubmitResult newInstance(ClientClearingConfirmResponse response) {
        if (response == null) {
            return DEFAULT;
        }
        return SettlementClearingSubmitResult
                .builder()
                .response(response)
                .build();
    }

    public boolean isSuccess() {
        return response != null && Objects.nonNull(response.getId());
    }
}