package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.fundclearing.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.BaseRequest;
import com.shouqianba.trade.fund.settlement.common.util.JsonUtils;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * 资金清分通知请求
 * <AUTHOR> Date: 2025/6/17 Time: 10:20 AM
 */
@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class FundClearingNotifyRequest extends BaseRequest {

    /**
     * 通知类型：1-资金渠道通知，2-清分通知
     */
    @JsonProperty("notify_type")
    private Integer notifyType;

    /**
     * 通知数据
     */
    @JsonProperty("notify_data")
    private Object notifyData;

    /**
     * 通知类型常量
     */
    public static final int NOTIFY_TYPE_FUND_CHANNEL_NOTIFY = 1;
    public static final int NOTIFY_TYPE_CLEARING_NOTIFY = 2;

    /**
     * 类型转换方法，与FundChannelNotifyRequest保持一致
     */
    public <T> T genTypeValue(Class<T> tClass) {
        return JsonUtils.convertToObject(notifyData, tClass);
    }

    public <T> T genTypeValue(TypeReference<T> reference) {
        return JsonUtils.convertToObject(notifyData, reference);
    }

    /**
     * 创建资金渠道通知请求
     */
    public static FundClearingNotifyRequest createFundChannelNotify(Object notifyData) {
        return FundClearingNotifyRequest.builder()
                .notifyType(NOTIFY_TYPE_FUND_CHANNEL_NOTIFY)
                .notifyData(notifyData)
                .build();
    }

    /**
     * 创建清分通知请求
     */
    public static FundClearingNotifyRequest createClearingNotify(Object notifyData) {
        return FundClearingNotifyRequest.builder()
                .notifyType(NOTIFY_TYPE_CLEARING_NOTIFY)
                .notifyData(notifyData)
                .build();
    }
}
