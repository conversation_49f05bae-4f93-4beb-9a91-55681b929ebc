package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req;

import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.BaseRequest;
import com.wosai.upay.brandsettle.request.WithdrawRequest;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * 品牌结算提现请求
 * <AUTHOR> Date: 2025/6/15 Time: 10:20 AM
 */
@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class SettlementWithdrawSubmitRequest extends BaseRequest {
    
    /**
     * 客户端流水号，代表唯一键
     */
    private String clientSn;
    
    /**
     * 提现金额（单位：分）
     */
    private Long amount;
    
    /**
     * 是否提现所有金额
     */
    private Boolean withdrawAllAmount;
    
    /**
     * 提现的商户ID
     */
    private String merchantId;
    
    public WithdrawRequest genWithdrawRequest() {
        WithdrawRequest request = new WithdrawRequest();
        request.setClientSn(this.clientSn);
        request.setAmount(this.amount);
        request.setWithdrawAllAmount(this.withdrawAllAmount);
        request.setMerchantId(this.merchantId);
        return request;
    }
}