package com.shouqianba.trade.fund.settlement.infrastructure.adapter.datasource.mysql.settlementflow.query;

import com.shouqianba.trade.fund.settlement.infrastructure.adapter.datasource.mysql.BaseQuery;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.query.SettlementFlowAggrQuery;
import lombok.Data;
import lombok.experimental.SuperBuilder;

/***
 * <AUTHOR> Date: 2025/04/24 Time: 04:34 PM
 */
@Data
@SuperBuilder(toBuilder = true)
public class SettlementFlowQuery extends BaseQuery {

    /**
     * 结算流水ID
     */
    private Long id;

    /**
     * 流水类型
     */
    private Byte type;

    /**
     * 交易流水号
     */
    private String transSn;

    /**
     * 业务订单号
     */
    private String orderSn;

    /**
     * 批次ID
     */
    private Long batchId;

    /**
     * 资金池ID
     */
    private Long poolId;

    /**
     * 付款方品牌编号
     */
    private String fromBrandSn;

    /**
     * 付款方商户编号
     */
    private String fromMerchantSn;

    /**
     * 付款方门店编号
     */
    private String fromStoreSn;

    /**
     * 收款方品牌编号
     */
    private String toBrandSn;

    /**
     * 收款方商户编号
     */
    private String toMerchantSn;

    /**
     * 收款方门店编号
     */
    private String toStoreSn;

    /**
     * 业务订单号
     */
    private String flowOrderNo;

    /**
     * 批次号
     */
    private String batchSn;

    /**
     * 付款方类型
     */
    private Byte fromType;

    /**
     * 付款方编号
     */
    private String fromSn;

    /**
     * 收款方类型
     */
    private Byte toType;

    /**
     * 收款方编号
     */
    private String toSn;

    /**
     * 状态
     */
    private Byte status;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方式
     */
    private String sort;

    /**
     * 查询数量
     */
    private Integer querySize;

    /**
     * 生成查询对象
     *
     * @param aggrQuery 聚合查询对象
     * @return 查询对象
     */
    public static SettlementFlowQuery genSettlementFlowQuery(SettlementFlowAggrQuery aggrQuery) {
        if (aggrQuery == null) {
            return null;
        }
        return SettlementFlowQuery
                .builder()
                .id(aggrQuery.getId())
                .type(aggrQuery.getType())
                .transSn(aggrQuery.getTransSn())
                .orderSn(aggrQuery.getOrderSn())
                .flowOrderNo(aggrQuery.getFlowOrderNo())
                .batchSn(aggrQuery.getBatchSn())
                .batchId(aggrQuery.getBatchId())
                .poolId(aggrQuery.getPoolId())
                .fromType(aggrQuery.getFromType())
                .fromSn(aggrQuery.getFromSn())
                .fromBrandSn(aggrQuery.getFromBrandSn())
                .fromMerchantSn(aggrQuery.getFromMerchantSn())
                .fromStoreSn(aggrQuery.getFromStoreSn())
                .toType(aggrQuery.getToType())
                .toSn(aggrQuery.getToSn())
                .toBrandSn(aggrQuery.getToBrandSn())
                .toMerchantSn(aggrQuery.getToMerchantSn())
                .toStoreSn(aggrQuery.getToStoreSn())
                .status(aggrQuery.getStatus())
                .sortField(aggrQuery.getSortField())
                .endCursor(aggrQuery.getEndCursor())
                .cursorField(aggrQuery.getCursorField())
                .isDesc(aggrQuery.isDesc())
                .querySize(aggrQuery.getQuerySize())
                .build();
    }

}