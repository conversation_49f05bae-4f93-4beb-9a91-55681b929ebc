# FundClearingClient 使用说明

## 概述

FundClearingClient 是一个用于调用资金清分通知接口的客户端，它使用 HttpClientFacade 进行 HTTP 调用，参照 FeishuClient 的设计模式，并遵循 CoreBusinessClient 的 request/result 规范。

## 主要功能

- 发送资金渠道通知
- 发送清分通知
- 调用 NotifyController 中的 fundNotify 方法

## 类结构

### FundClearingClient
主要的客户端类，提供 `fundNotify` 方法用于发送通知。

### FundClearingNotifyRequest
请求类，继承自 BaseRequest，包含：
- `notifyType`: 通知类型（1-资金渠道通知，2-清分通知）
- `notifyData`: 通知数据

### FundClearingNotifyResult
结果类，包含：
- `result`: 调用结果字符串，"success" 表示成功，其他字符串表示失败原因

## 使用示例

### 1. 发送资金渠道通知

```java
@Resource
private FundClearingClient fundClearingClient;

public void sendFundChannelNotify() {
    // 构造通知数据
    Map<String, Object> notifyData = new HashMap<>();
    notifyData.put("brand_id", "BRAND123");
    notifyData.put("action_id", "ACTION456");
    notifyData.put("settlement_channel", 1);
    notifyData.put("amount", 100000L);
    notifyData.put("entry_time", System.currentTimeMillis());

    // 创建请求
    FundClearingNotifyRequest request = FundClearingNotifyRequest
            .createFundChannelNotify(notifyData);

    // 发送通知
    FundClearingNotifyResult result = fundClearingClient.fundNotify(request);

    // 处理结果
    if (result.isSuccess()) {
        log.info("通知发送成功，结果: {}", result.getResult());
    } else {
        log.error("通知发送失败，结果: {}", result.getResult());
    }
}
```

### 2. 发送清分通知

```java
public void sendClearingNotify() {
    // 构造通知数据
    Map<String, Object> notifyData = new HashMap<>();
    notifyData.put("brand_id", "BRAND123");
    notifyData.put("date", "2025-06-17");
    notifyData.put("action_id", "CLEARING789");
    notifyData.put("client_sn", "CLIENT001");
    notifyData.put("status", 1);
    notifyData.put("finish_time", System.currentTimeMillis());
    notifyData.put("clearing_result_file", "result.csv");

    // 创建请求
    FundClearingNotifyRequest request = FundClearingNotifyRequest
            .createClearingNotify(notifyData);

    // 发送通知
    FundClearingNotifyResult result = fundClearingClient.fundNotify(request);
    
    // 检查结果（失败时抛出异常）
    result.checkSuccess();
    
    // 或者直接检查结果字符串
    if ("success".equals(result.getResult())) {
        log.info("清分通知发送成功");
    } else {
        log.error("清分通知发送失败: {}", result.getResult());
    }
}
```

### 3. 使用现有数据模型

```java
public void sendNotifyWithDataModel() {
    // 可以直接传入 FundChannelNotifyDataModel 或 FundClearingNotifyDataModel
    FundChannelNotifyDataModel dataModel = new FundChannelNotifyDataModel();
    dataModel.setBrandId("BRAND123");
    dataModel.setActionId("ACTION456");
    // ... 设置其他字段

    FundClearingNotifyRequest request = FundClearingNotifyRequest
            .createFundChannelNotify(dataModel);

    FundClearingNotifyResult result = fundClearingClient.fundNotify(request);
    
    // 简单的结果处理
    log.info("通知结果: {}", result.getResult());
}
```

## 配置

在 application.yml 中配置通知接口地址：

```yaml
fund:
  clearing:
    notify:
      url: http://localhost:8080/fund-bill/notify/fund-notify
```

## 设计特点

1. **参照 FeishuClient**：使用 HttpClientFacade 进行 HTTP 调用
2. **遵循 CoreBusinessClient 规范**：使用 InvokeProcessor.process 模板进行调用
3. **兼容现有接口**：与 NotifyController 的 fundNotify 方法完全兼容
4. **简化的结果处理**：Result 只返回一个字符串，"success" 表示成功，其他表示失败原因
5. **详细的日志记录**：记录请求、响应和异常信息

## 注意事项

1. 确保目标服务的 `/fund-bill/notify/fund-notify` 接口可用
2. 通知数据格式需要与 FundChannelNotifyRequest 兼容
3. 建议在生产环境中配置合适的超时时间和重试机制
4. 使用 `result.checkSuccess()` 方法可以在失败时直接抛出异常
5. 结果字符串为 "success" 时表示调用成功，其他任何字符串都表示失败
