package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req;

import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.BaseRequest;
import com.wosai.upay.brandsettle.request.AccountWalletQueryRequest;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * 品牌结算账户余额查询请求
 *
 * <AUTHOR> Date: 2025/7/15 Time: 10:20 AM
 */
@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class SettlementAccountWalletQueryRequest extends BaseRequest {

    /**
     * 商户ID
     */
    private String merchantId;

    /**
     * 品牌ID
     */
    private String brandId;

    /**
     * 外部商户编号
     */
    private String outMerchantNo;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 账户类型
     */
    private Integer accountType;

    /**
     * 生成底层查询请求
     */
    public AccountWalletQueryRequest genAccountWalletQueryRequest() {
        AccountWalletQueryRequest request = new AccountWalletQueryRequest();
        request.setMerchantId(this.merchantId);
        request.setBrandId(this.brandId);
        request.setOutMerchantNo(this.outMerchantNo);
        request.setSn(this.merchantSn);
        request.setAccountType(this.accountType);
        return request;
    }
}