package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res;

import com.wosai.upay.brandsettle.response.ListResult;
import com.wosai.upay.brandsettle.response.WithdrawInfo;
import lombok.Builder;
import lombok.Getter;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 品牌结算提现列表查询结果
 *
 * <AUTHOR> Date: 2025/6/15 Time: 10:20 AM
 */
@Getter
@Builder
public class SettlementWithdrawInfoListResult {
    public static final SettlementWithdrawInfoListResult DEFAULT = SettlementWithdrawInfoListResult.builder()
            .total(0L)
            .withdrawInfos(Collections.emptyList())
            .build();

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 提现记录列表
     */
    private List<WithdrawInfoResult> withdrawInfos;

    /**
     * 创建WithdrawInfoListResult实例
     *
     * @param listResult 响应
     * @return WithdrawInfoListResult实例
     */
    public static SettlementWithdrawInfoListResult newInstance(ListResult<WithdrawInfo> listResult) {
        if (Objects.isNull(listResult) || Objects.isNull(listResult.getRecords())) {
            return DEFAULT;
        }

        List<WithdrawInfoResult> withdrawInfoResults = listResult.getRecords().stream()
                .map(WithdrawInfoResult::newInstance)
                .collect(Collectors.toList());

        return SettlementWithdrawInfoListResult.builder()
                .total(listResult.getTotal())
                .withdrawInfos(withdrawInfoResults)
                .build();
    }

    /**
     * 提现信息结果
     */
    @Getter
    @Builder
    public static class WithdrawInfoResult {

        /**
         * 提现单号
         */
        private String withdrawSn;

        /**
         * 提现状态
         */
        private Byte status;

        /**
         * 提现金额
         */
        private Long amount;

        /**
         * 商户ID
         */
        private String merchantId;

        /**
         * 创建时间
         */
        private Long createTime;

        /**
         * 创建WithdrawInfoResult实例
         *
         * @param withdrawInfo 提现信息
         * @return WithdrawInfoResult实例
         */
        public static WithdrawInfoResult newInstance(WithdrawInfo withdrawInfo) {
            if (withdrawInfo == null) {
                return null;
            }

            return WithdrawInfoResult.builder()
                    .build();
        }
    }
}