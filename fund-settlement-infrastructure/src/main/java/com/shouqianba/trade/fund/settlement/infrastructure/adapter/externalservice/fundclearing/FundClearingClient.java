package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.fundclearing;

import com.shouqianba.trade.fund.settlement.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.settlement.common.template.InvokeTemplate;
import com.shouqianba.trade.fund.settlement.common.util.JsonUtils;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.fundclearing.model.req.FundClearingNotifyRequest;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.fundclearing.model.res.FundClearingNotifyResult;
import com.shouqianba.trade.fund.settlement.infrastructure.support.HttpClientFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 资金清分客户端
 * <AUTHOR> Date: 2025/6/17 Time: 10:20 AM
 */
@Slf4j
@Component
public class FundClearingClient {
    
    private static final String CONTENT_TYPE = "application/json;charset=utf-8";
    
    @Resource
    private HttpClientFacade httpClientFacade;
    
    @Value("${fund.clearing.notify.url:http://localhost:8080/fund-bill/notify/fund-notify}")
    private String fundNotifyUrl;

    /**
     * 发送资金清分通知
     * 
     * @param request 资金清分通知请求
     * @return 资金清分通知结果
     */
    public FundClearingNotifyResult fundNotify(FundClearingNotifyRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected FundClearingNotifyResult invoke(FundClearingNotifyRequest request) throws Throwable {
                log.info("[资金清分通知]>>>>>>入参: {}", request.toJsonString());
                
                String requestBody = request.toJsonString();
                HttpClientFacade.HttpResult httpResult = httpClientFacade.core()
                        .post(fundNotifyUrl, requestBody, CONTENT_TYPE);
                
                log.info("[资金清分通知]>>>>>>HTTP响应: code={}, body={}, timeConsuming={}ms", 
                        httpResult.getCode(), httpResult.getBody(), httpResult.getTimeConsuming());
                
                FundClearingNotifyResult result = FundClearingNotifyResult.from(httpResult);
                log.info("[资金清分通知]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));
                
                return result;
            }

            @Override
            protected FundClearingNotifyResult onFailure(FundClearingNotifyRequest request, Throwable throwable) {
                log.error("[资金清分通知]>>>>>>调用异常, 请求: {}, 异常栈: ", request.toJsonString(), throwable);
                return FundClearingNotifyResult.failure("调用异常: " + throwable.getMessage());
            }
        });
    }
}
