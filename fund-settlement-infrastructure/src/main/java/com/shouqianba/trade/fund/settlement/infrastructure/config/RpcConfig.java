package com.shouqianba.trade.fund.settlement.infrastructure.config;

import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.shouqianba.trade.fund.core.api.ChannelConfigService;
import com.wosai.authorize.service.DouYinAuthorizeService;
import com.wosai.cua.brand.business.api.facade.BrandFacade;
import com.wosai.trade.service.TradeStateService;
import com.wosai.upay.brandsettle.service.AccountWalletService;
import com.wosai.upay.brandsettle.service.AdjustService;
import com.wosai.upay.brandsettle.service.WithdrawService;
import com.wosai.upay.brandsettle.service.v2.ClientClearingService;
import com.wosai.upay.core.service.StoreService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> Date: 2023/6/21 Time: 10:57
 */
@Configuration
public class RpcConfig {

    @Bean
    public JsonProxyFactoryBean brandFacade(@Value("${service.rpc.brand-business}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/brand");
        jsonProxyFactoryBean.setServiceInterface(BrandFacade.class);
        jsonProxyFactoryBean.setServerName("brand-business");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean tradeStateService(@Value("${service.rpc.trade-manage}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/trade_state");
        jsonProxyFactoryBean.setServiceInterface(TradeStateService.class);
        jsonProxyFactoryBean.setServerName("trade-manage-service");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean channelConfigService(@Value("${service.rpc.fund-core}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/channel");
        jsonProxyFactoryBean.setServiceInterface(ChannelConfigService.class);
        jsonProxyFactoryBean.setServerName("fund-core");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(10000);
        jsonProxyFactoryBean.setReadTimeoutMillis(30000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean douYinAuthorizeService(@Value("${service.rpc.scorpio}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/douyin");
        jsonProxyFactoryBean.setServiceInterface(DouYinAuthorizeService.class);
        jsonProxyFactoryBean.setServerName("scorpio");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean storeService(@Value("${service.rpc.core-business}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/store");
        jsonProxyFactoryBean.setServiceInterface(StoreService.class);
        jsonProxyFactoryBean.setServerName("core-business");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean withdrawService(@Value("${service.rpc.brand-settle}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/withdraw");
        jsonProxyFactoryBean.setServiceInterface(WithdrawService.class);
        jsonProxyFactoryBean.setServerName("brand-settle");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean accountWalletService(@Value("${service.rpc.brand-settle}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/account");
        jsonProxyFactoryBean.setServiceInterface(AccountWalletService.class);
        jsonProxyFactoryBean.setServerName("brand-settle");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean clientClearingService(@Value("${service.rpc.brand-settle}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/clientClearing");
        jsonProxyFactoryBean.setServiceInterface(ClientClearingService.class);
        jsonProxyFactoryBean.setServerName("brand-settle");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean adjustService(@Value("${service.rpc.brand-settle}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/adjust");
        jsonProxyFactoryBean.setServiceInterface(AdjustService.class);
        jsonProxyFactoryBean.setServerName("brand-settle");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }
}
