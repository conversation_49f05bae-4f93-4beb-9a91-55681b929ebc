package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.fundclearing.model;

import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.fundclearing.FundClearingClient;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.fundclearing.model.req.FundClearingNotifyRequest;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.fundclearing.model.res.FundClearingNotifyResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * FundClearingClient使用示例
 * <AUTHOR> Date: 2025/6/17 Time: 10:20 AM
 */
@Slf4j
@Component
public class FundClearingClientUsageExample {

    @Resource
    private FundClearingClient fundClearingClient;

    /**
     * 发送资金渠道通知示例
     */
    public void sendFundChannelNotifyExample() {
        // 构造资金渠道通知数据
        Map<String, Object> fundChannelData = new HashMap<>();
        fundChannelData.put("brand_id", "BRAND123");
        fundChannelData.put("action_id", "ACTION456");
        fundChannelData.put("settlement_channel", 1);
        fundChannelData.put("amount", 100000L); // 1000.00元，单位：分
        fundChannelData.put("entry_time", System.currentTimeMillis());

        // 创建请求
        FundClearingNotifyRequest request = FundClearingNotifyRequest
                .createFundChannelNotify(fundChannelData);

        // 发送通知
        FundClearingNotifyResult result = fundClearingClient.fundNotify(request);

        // 处理结果 - 简化版本，只返回字符串
        if (result.isSuccess()) {
            log.info("资金渠道通知发送成功，结果: {}", result.getResult());
        } else {
            log.error("资金渠道通知发送失败，结果: {}", result.getResult());
        }
    }

    /**
     * 发送清分通知示例
     */
    public void sendClearingNotifyExample() {
        // 构造清分通知数据
        Map<String, Object> clearingData = new HashMap<>();
        clearingData.put("brand_id", "BRAND123");
        clearingData.put("date", "2025-06-17");
        clearingData.put("action_id", "CLEARING789");
        clearingData.put("client_sn", "CLIENT001");
        clearingData.put("status", 1); // 1-成功，2-失败
        clearingData.put("finish_time", System.currentTimeMillis());
        clearingData.put("clearing_result_file", "clearing_result_20250617.csv");

        // 创建请求
        FundClearingNotifyRequest request = FundClearingNotifyRequest
                .createClearingNotify(clearingData);

        // 发送通知
        FundClearingNotifyResult result = fundClearingClient.fundNotify(request);

        // 处理结果
        if ("success".equals(result.getResult())) {
            log.info("清分通知发送成功");
        } else {
            log.error("清分通知发送失败: {}", result.getResult());
        }
    }

    /**
     * 使用具体的数据模型发送通知
     */
    public void sendNotifyWithDataModel() {
        // 可以直接使用现有的数据模型
        // 例如：FundChannelNotifyDataModel 或 FundClearingNotifyDataModel
        
        // 示例：使用Map构造数据，实际使用时可以直接传入数据模型对象
        Map<String, Object> notifyData = new HashMap<>();
        notifyData.put("brand_id", "BRAND123");
        notifyData.put("action_id", "ACTION456");
        // ... 其他字段

        FundClearingNotifyRequest request = FundClearingNotifyRequest.builder()
                .notifyType(FundClearingNotifyRequest.NOTIFY_TYPE_FUND_CHANNEL_NOTIFY)
                .notifyData(notifyData)
                .build();

        FundClearingNotifyResult result = fundClearingClient.fundNotify(request);
        
        // 检查结果（失败时抛出异常）
        try {
            result.checkSuccess(); // 如果失败会抛出异常
            log.info("通知发送成功");
        } catch (RuntimeException e) {
            log.error("通知发送失败: {}", e.getMessage());
        }
        
        // 或者直接检查结果字符串
        log.info("通知结果: {}", result.getResult());
    }
}
