package com.shouqianba.trade.fund.settlement.infrastructure.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.wosai.middleware.aliyun.oss.DynamicCredentialsProvider;
import com.wosai.middleware.vault.Vault;
import lombok.Data;
import lombok.SneakyThrows;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> Date: 2025/6/17 Time: 16:20 PM
 */
@Configuration
@EnableConfigurationProperties({AliyunOssConfig.OssProperties.class})
public class AliyunOssConfig {

    private final OssProperties ossProperties;

    public AliyunOssConfig(OssProperties ossProperties) {
        this.ossProperties = ossProperties;
    }


    @Bean
    @SneakyThrows
    public CredentialsProvider credentialsProvider() {
        return new DynamicCredentialsProvider(Vault.load(ossProperties.getGroupName(), null));
    }

    @Bean
    public OSS ossClient() {
        return new OSSClientBuilder().build(ossProperties.getEndpoint(), credentialsProvider());
    }


    @Data
    @ConfigurationProperties(prefix = "aliyun.oss")
    public static class OssProperties {
        private String endpoint;
        private String groupName;
    }
}
