package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req;

import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.BaseRequest;
import com.wosai.upay.brandsettle.request.TransferRequest;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * 品牌结算转账请求
 */
@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class SettlementTransferRequest extends BaseRequest {
    
    /**
     * 客户端流水号
     */
    private String clientSn;
    
    /**
     * 出资方，收付通商户号
     */
    private String sourceMerchantSn;
    
    /**
     * 收资方，收付通商户号
     */
    private String targetMerchantSn;
    
    /**
     * 品牌ID
     */
    private String brandId;
    
    /**
     * 转账金额（分）
     */
    private Long amount;
    
    public TransferRequest genTransferRequest() {
        TransferRequest request = new TransferRequest();
        request.setClientSn(this.clientSn);
        request.setSourceMerchantSn(this.sourceMerchantSn);
        request.setTargetMerchantSn(this.targetMerchantSn);
        request.setBrandId(this.brandId);
        request.setAmount(this.amount);
        return request;
    }
}