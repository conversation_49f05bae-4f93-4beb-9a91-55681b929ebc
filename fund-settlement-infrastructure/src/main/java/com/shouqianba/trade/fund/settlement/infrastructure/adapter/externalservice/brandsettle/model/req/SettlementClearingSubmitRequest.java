package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req;

import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.BaseRequest;
import com.wosai.upay.brandsettle.request.clearing.ClearingSubmitRequest;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * 品牌结算客户端
 * <AUTHOR> Date: 2025/6/13 Time: 10:20 AM
 */
@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class SettlementClearingSubmitRequest extends BaseRequest {

    private String brandId;
    private String date;
    private Integer fundChannel;
    private Integer settlementChannel;
    private String clientSn;
    private String clearingFile;
    private String bucketName;

    public ClearingSubmitRequest genClearingSubmitRequest() {
        return new ClearingSubmitRequest()
                .setBrandId(this.brandId)
                .setDate(this.date)
                .setFundChannel(this.fundChannel)
                .setSettlementChannel(this.settlementChannel)
                .setClientSn(this.clientSn)
                .setClearingFile(this.clearingFile)
                .setBucketName(this.bucketName);
    }
}