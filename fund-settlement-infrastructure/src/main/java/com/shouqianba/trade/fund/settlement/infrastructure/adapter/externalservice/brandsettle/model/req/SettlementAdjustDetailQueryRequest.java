package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req;

import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.BaseRequest;
import com.wosai.upay.brandsettle.request.AdjustDetailQueryRequest;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * 品牌结算调账详情查询请求
 */
@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class SettlementAdjustDetailQueryRequest extends BaseRequest {
    
    /**
     * 转账发起的唯一流水号，与 adjustSn 二选一
     */
    private String clientSn;
    
    /**
     * 转账流水号（与 clientSn 二选一）
     */
    private String adjustSn;
    
    /**
     * 品牌唯一标识
     */
    private String brandId;
    
    public AdjustDetailQueryRequest genAdjustDetailQueryRequest() {
        AdjustDetailQueryRequest request = new AdjustDetailQueryRequest();
        request.setClientSn(this.clientSn);
        request.setAdjustSn(this.adjustSn);
        request.setBrandId(this.brandId);
        return request;
    }
}