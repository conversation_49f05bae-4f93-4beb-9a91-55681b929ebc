package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req;

import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.BaseRequest;
import com.wosai.upay.brandsettle.request.WithdrawQueryRequest;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * 品牌结算提现查询请求
 *
 * <AUTHOR> Date: 2025/6/15 Time: 10:20 AM
 */
@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class SettlementWithdrawQueryRequest extends BaseRequest {
    private String brandId;
    private String merchantSn;
    private String clientSn;
    private String sn;
    private String id;
    private String merchantId;
    private String token;
    private String accountId;
    private String accountName;
    private String merchantUserId;
    private Integer page;
    private Integer pageSize;
    private Long startTime;
    private Long endTime;
    private String OrderByField;
    private Integer order;
    private Boolean searchCount;

    /**
     * 生成底层查询请求
     */
    public WithdrawQueryRequest genWithdrawQueryRequest() {
        WithdrawQueryRequest request = new WithdrawQueryRequest();
        request.setClientSn(this.clientSn);
        request.setMerchantId(this.merchantId);
        request.setBrandId(this.brandId);
        request.setMerchantSn(this.merchantSn);
        request.setPage(this.page);
        request.setPageSize(this.pageSize);
        request.setStartTime(this.startTime);
        request.setEndTime(this.endTime);

        com.wosai.upay.brandsettle.request.PageInfo.OrderBy orderBy =
                new com.wosai.upay.brandsettle.request.PageInfo.OrderBy(this.OrderByField,
                        com.wosai.upay.brandsettle.request.PageInfo.OrderBy.OrderType.fromNumber(this.order));
        request.setOrderBy(orderBy);
        return request;
    }


}