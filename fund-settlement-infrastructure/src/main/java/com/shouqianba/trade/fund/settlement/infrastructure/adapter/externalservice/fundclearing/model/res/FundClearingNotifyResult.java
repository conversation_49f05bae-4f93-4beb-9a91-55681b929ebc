package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.fundclearing.model.res;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shouqianba.trade.fund.settlement.infrastructure.support.HttpClientFacade;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 资金清分通知结果
 * <AUTHOR> Date: 2025/6/17 Time: 10:20 AM
 */
@Getter
@Builder(access = AccessLevel.PROTECTED)
public class FundClearingNotifyResult {

    public static final FundClearingNotifyResult DEFAULT = FundClearingNotifyResult.builder().build();
    
    private static final String SUCCESS_RESPONSE = "success";
    private static final String FAILED_RESPONSE = "failed";

    /**
     * 是否调用成功
     */
    private Boolean isSuccessful;

    /**
     * HTTP状态码
     */
    private Integer httpCode;

    /**
     * 响应内容
     */
    private String responseBody;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 调用耗时（毫秒）
     */
    private Long timeConsuming;

    /**
     * 从HTTP结果创建
     */
    public static FundClearingNotifyResult from(HttpClientFacade.HttpResult httpResult) {
        if (Objects.isNull(httpResult)) {
            return failure("HTTP结果为空");
        }

        boolean isBusinessSuccess = httpResult.isSuccessful() && 
                StringUtils.equals(SUCCESS_RESPONSE, httpResult.getBody());

        return FundClearingNotifyResult.builder()
                .isSuccessful(isBusinessSuccess)
                .httpCode(httpResult.getCode())
                .responseBody(httpResult.getBody())
                .timeConsuming(httpResult.getTimeConsuming())
                .errorMessage(isBusinessSuccess ? null : 
                        String.format("HTTP调用失败: code=%d, body=%s", 
                                httpResult.getCode(), httpResult.getBody()))
                .build();
    }

    /**
     * 创建失败结果
     */
    public static FundClearingNotifyResult failure(String errorMessage) {
        return FundClearingNotifyResult.builder()
                .isSuccessful(false)
                .errorMessage(errorMessage)
                .build();
    }

    /**
     * 创建成功结果
     */
    public static FundClearingNotifyResult success() {
        return FundClearingNotifyResult.builder()
                .isSuccessful(true)
                .responseBody(SUCCESS_RESPONSE)
                .build();
    }

    @JsonIgnore
    public boolean isNotSuccessful() {
        return !Boolean.TRUE.equals(isSuccessful);
    }

    @JsonIgnore
    public void checkSuccess() {
        if (isNotSuccessful()) {
            throw new RuntimeException("资金清分通知调用失败: " + errorMessage);
        }
    }
}
