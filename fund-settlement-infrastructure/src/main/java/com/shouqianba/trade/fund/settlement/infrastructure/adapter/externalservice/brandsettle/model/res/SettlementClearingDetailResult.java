package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res;

import com.wosai.upay.brandsettle.request.clearing.ClientClearingConfirmResponse;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;

import java.util.Objects;

/**
 * 品牌结算客户端
 *
 * <AUTHOR> Date: 2025/6/13 Time: 10:20 AM
 */
@Getter
@Builder(access = AccessLevel.PROTECTED)
public class SettlementClearingDetailResult {
    public static final SettlementClearingDetailResult DEFAULT = SettlementClearingDetailResult
            .builder()
            .build();

    private final ClientClearingConfirmResponse response;

    public static SettlementClearingDetailResult newInstance(ClientClearingConfirmResponse response) {
        if (response == null) {
            return DEFAULT;
        }
        return SettlementClearingDetailResult
                .builder()
                .response(response)
                .build();
    }

    public boolean isSuccess() {
        return Objects.nonNull(response) && Objects.nonNull(response.getId());
    }
}