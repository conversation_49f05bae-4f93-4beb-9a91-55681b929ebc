package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle;

import com.shouqianba.trade.fund.settlement.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.settlement.common.template.InvokeTemplate;
import com.shouqianba.trade.fund.settlement.common.util.JsonUtils;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req.*;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res.*;
import com.wosai.upay.brandsettle.request.TransferRequest;
import com.wosai.upay.brandsettle.request.clearing.ClearingSubmitRequest;
import com.wosai.upay.brandsettle.request.clearing.ClientClearingConfirmResponse;
import com.wosai.upay.brandsettle.response.*;
import com.wosai.upay.brandsettle.service.AccountWalletService;
import com.wosai.upay.brandsettle.service.AdjustService;
import com.wosai.upay.brandsettle.service.WithdrawService;
import com.wosai.upay.brandsettle.service.v2.ClientClearingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 品牌结算客户端
 * <AUTHOR> Date: 2025/6/13 Time: 10:20 AM
 */
@Slf4j
@Component
public class BrandSettleClient {

    @Resource
    private WithdrawService withdrawService;
    @Resource
    private AccountWalletService accountWalletService;
    @Resource
    private ClientClearingService clientClearingService;
    @Resource
    private AdjustService adjustService;

    /**
     * 提交清分申请
     */
    public SettlementClearingSubmitResult submitClearing(SettlementClearingSubmitRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected SettlementClearingSubmitResult invoke(SettlementClearingSubmitRequest request) {
                ClearingSubmitRequest clearingSubmitRequest = request.genClearingSubmitRequest();
                log.info("[品牌结算提交清分申请]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(clearingSubmitRequest));
                ClientClearingConfirmResponse result = clientClearingService.submit(clearingSubmitRequest);
                log.info("[品牌结算提交清分申请]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));
                return SettlementClearingSubmitResult.newInstance(result);
            }

            @Override
            protected SettlementClearingSubmitResult onFailure(SettlementClearingSubmitRequest request, Throwable throwable) {
                log.error("[品牌结算提交清分申请]>>>>>>异常栈:", throwable);
                return SettlementClearingSubmitResult.DEFAULT;
            }
        });
    }

    /**
     * 查询清分明细
     */
    public SettlementClearingDetailResult queryClearingDetail(SettlementClearingDetailRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected SettlementClearingDetailResult invoke(SettlementClearingDetailRequest request) {
                String clientSn = request.getClientSn();
                log.info("[品牌结算查询清分明细]>>>>>>入参: {}", clientSn);
                ClientClearingConfirmResponse result = clientClearingService.detail(clientSn);
                log.info("[品牌结算查询清分明细]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));
                return SettlementClearingDetailResult.newInstance(result);
            }

            @Override
            protected SettlementClearingDetailResult onFailure(SettlementClearingDetailRequest request, Throwable throwable) {
                log.error("[品牌结算查询清分明细]>>>>>>异常栈:", throwable);
                return SettlementClearingDetailResult.DEFAULT;
            }
        });
    }

    /**
     * 提交提现申请
     *
     * @param request 提现请求
     * @return 提现结果
     */
    public SettlementWithdrawSubmitResult submitWithdraw(SettlementWithdrawSubmitRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected SettlementWithdrawSubmitResult invoke(SettlementWithdrawSubmitRequest request) {
                com.wosai.upay.brandsettle.request.WithdrawRequest withdrawRequest = request.genWithdrawRequest();
                log.info("[品牌结算提交提现申请]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(withdrawRequest));
                WithdrawResponse result = withdrawService.submitWithdraw(withdrawRequest);
                log.info("[品牌结算提交提现申请]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));
                return SettlementWithdrawSubmitResult.newInstance(result);
            }

            @Override
            protected SettlementWithdrawSubmitResult onFailure(SettlementWithdrawSubmitRequest request, Throwable throwable) {
                log.error("[品牌结算提交提现申请]>>>>>>异常栈:", throwable);
                return SettlementWithdrawSubmitResult.DEFAULT;
            }
        });
    }

    /**
     * 查询提现详情
     *
     * @param request 查询请求
     * @return 提现查询结果
     */
    public SettlementWithdrawQueryResult queryWithdraw(SettlementWithdrawQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected SettlementWithdrawQueryResult invoke(SettlementWithdrawQueryRequest request) {
                com.wosai.upay.brandsettle.request.WithdrawQueryRequest queryRequest = request.genWithdrawQueryRequest();
                log.info("[品牌结算查询提现详情]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(queryRequest));
                WithdrawQueryResponse result = withdrawService.queryWithdraw(queryRequest);
                log.info("[品牌结算查询提现详情]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));
                return SettlementWithdrawQueryResult.newInstance(result);
            }

            @Override
            protected SettlementWithdrawQueryResult onFailure(SettlementWithdrawQueryRequest request, Throwable throwable) {
                log.error("[品牌结算查询提现详情]>>>>>>异常栈:", throwable);
                return SettlementWithdrawQueryResult.DEFAULT;
            }
        });
    }

    /**
     * 查询提现列表
     *
     * @param request 查询请求
     * @return 提现列表结果
     */
    public SettlementWithdrawInfoListResult getWithdraws(SettlementWithdrawQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected SettlementWithdrawInfoListResult invoke(SettlementWithdrawQueryRequest request) {
                com.wosai.upay.brandsettle.request.WithdrawQueryRequest queryRequest = request.genWithdrawQueryRequest();
                log.info("[品牌结算查询提现列表]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(queryRequest));
                ListResult<WithdrawInfo> result = withdrawService.getWithdraws(queryRequest);
                log.info("[品牌结算查询提现列表]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));
                return SettlementWithdrawInfoListResult.newInstance(result);
            }

            @Override
            protected SettlementWithdrawInfoListResult onFailure(SettlementWithdrawQueryRequest request, Throwable throwable) {
                log.error("[品牌结算查询提现列表]>>>>>>异常栈:", throwable);
                return SettlementWithdrawInfoListResult.DEFAULT;
            }
        });
    }

    /**
     * 查询账户余额
     *
     * @param request 查询请求
     * @return 账户余额结果
     */
    public SettlementAccountWalletResult getWallet(SettlementAccountWalletQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected SettlementAccountWalletResult invoke(SettlementAccountWalletQueryRequest request) {
                com.wosai.upay.brandsettle.request.AccountWalletQueryRequest queryRequest = request.genAccountWalletQueryRequest();
                log.info("[品牌结算查询账户余额]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(queryRequest));
                AccountWalletResponse result = accountWalletService.getWallet(queryRequest);
                log.info("[品牌结算查询账户余额]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));
                return SettlementAccountWalletResult.newInstance(result);
            }

            @Override
            protected SettlementAccountWalletResult onFailure(SettlementAccountWalletQueryRequest request, Throwable throwable) {
                log.error("[品牌结算查询账户余额]>>>>>>异常栈:", throwable);
                return SettlementAccountWalletResult.DEFAULT;
            }
        });
    }

    /**
     * 品牌结算转账
     *
     * @param request 转账请求
     * @return 转账结果
     */
    public SettlementTransferResult transfer(SettlementTransferRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected SettlementTransferResult invoke(SettlementTransferRequest request) {
                TransferRequest transferRequest = request.genTransferRequest();
                log.info("[品牌结算转账]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(transferRequest));
                TransferResponse result = adjustService.transfer(transferRequest);
                log.info("[品牌结算转账]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));
                return SettlementTransferResult.newInstance(result);
            }

            @Override
            protected SettlementTransferResult onFailure(SettlementTransferRequest request, Throwable throwable) {
                log.error("[品牌结算转账]>>>>>>异常栈:", throwable);
                return SettlementTransferResult.DEFAULT;
            }
        });
    }

    /**
     * 查询调账详情
     *
     * @param request 查询请求
     * @return 调账详情结果
     */
    public SettlementAdjustDetailResult getAdjustDetail(SettlementAdjustDetailQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected SettlementAdjustDetailResult invoke(SettlementAdjustDetailQueryRequest request) {
                com.wosai.upay.brandsettle.request.AdjustDetailQueryRequest queryRequest = request.genAdjustDetailQueryRequest();
                log.info("[品牌结算查询调账详情]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(queryRequest));
                Adjust result = adjustService.getAdjustDetail(queryRequest);
                log.info("[品牌结算查询调账详情]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));
                return SettlementAdjustDetailResult.newInstance(result);
            }

            @Override
            protected SettlementAdjustDetailResult onFailure(SettlementAdjustDetailQueryRequest request, Throwable throwable) {
                log.error("[品牌结算查询调账详情]>>>>>>异常栈:", throwable);
                return SettlementAdjustDetailResult.DEFAULT;
            }
        });
    }
}
