package com.shouqianba.trade.fund.settlement.infrastructure.adapter.datasource.mysql.settlementstatistics;


import com.shouqianba.trade.fund.settlement.infrastructure.adapter.datasource.mysql.settlementstatistics.po.SettlementStatisticsPO;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.datasource.mysql.settlementstatistics.query.SettlementStatisticsQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/***
 * 结算统计DAO
 * <AUTHOR> Date: 2025/07/15 Time: 10:34 AM
 */
@Mapper
public interface SettlementStatisticsDao {

    /**
     * 插入
     *
     * @param po 结算统计PO
     * @return 影响行数
     */
    int insert(SettlementStatisticsPO po);


    /**
     * 更新
     *
     * @param po 结算统计PO
     * @return 影响行数
     */
    int update(SettlementStatisticsPO po);

    /**
     * 查询
     *
     * @param query 查询条件
     * @return 结算统计PO
     */
    SettlementStatisticsPO select(SettlementStatisticsQuery query);


    /**
     * 批量查询
     *
     * @param query 查询条件
     * @return 结算统计PO列表
     */
    List<SettlementStatisticsPO> batchSelect(SettlementStatisticsQuery query);
}