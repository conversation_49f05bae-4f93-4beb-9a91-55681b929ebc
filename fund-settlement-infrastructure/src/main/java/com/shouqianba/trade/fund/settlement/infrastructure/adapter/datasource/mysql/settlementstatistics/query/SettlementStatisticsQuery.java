package com.shouqianba.trade.fund.settlement.infrastructure.adapter.datasource.mysql.settlementstatistics.query;

import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.query.SettlementStatisticsAggrQuery;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.Objects;

/***
 * 结算统计查询对象
 * <AUTHOR> Date: 2025/07/15 Time: 10:34 AM
 */
@Data
@Builder
public class SettlementStatisticsQuery {

    /**
     * 结算统计ID
     */
    private Long id;

    /**
     * 统计类型
     */
    private Byte type;

    /**
     * 收款方主体类型
     */
    private Byte payeeType;

    /**
     * 品牌编号
     */
    private String brandSn;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 门店编号
     */
    private String storeSn;

    /**
     * 收单机构
     */
    private Integer acquiringCompany;

    /**
     * 统计日期
     */
    private LocalDate statisticsDate;

    /**
     * 统计日期-开始
     */
    private LocalDate statisticsDateStart;

    /**
     * 统计日期-结束
     */
    private LocalDate statisticsDateEnd;

    /**
     * 创建时间-开始
     */
    private LocalDate ctimeStart;

    /**
     * 创建时间-结束
     */
    private LocalDate ctimeEnd;

    /**
     * 分页-起始位置
     */
    private Integer offset;

    /**
     * 分页-每页大小
     */
    private Integer pageSize;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方式
     */
    private String sort;

    /**
     * 生成查询对象
     *
     * @param aggrQuery 聚合查询对象
     * @return 查询对象
     */
    public static SettlementStatisticsQuery genSettlementStatisticsQuery(SettlementStatisticsAggrQuery aggrQuery) {
        if (Objects.isNull(aggrQuery)) {
            return null;
        }
        SettlementStatisticsQueryBuilder builder = SettlementStatisticsQuery.builder()
                .id(aggrQuery.getId())
                .type(aggrQuery.getType())
                .payeeType(aggrQuery.getPayeeType())
                .brandSn(aggrQuery.getBrandSn())
                .merchantSn(aggrQuery.getMerchantSn())
                .storeSn(aggrQuery.getStoreSn())
                .acquiringCompany(aggrQuery.getAcquiringCompany())
                .statisticsDateStart(aggrQuery.getStatisticsDateStart())
                .statisticsDateEnd(aggrQuery.getStatisticsDateEnd())
                .ctimeStart(aggrQuery.getCtimeStart())
                .ctimeEnd(aggrQuery.getCtimeEnd());
        return builder.build();
    }
}