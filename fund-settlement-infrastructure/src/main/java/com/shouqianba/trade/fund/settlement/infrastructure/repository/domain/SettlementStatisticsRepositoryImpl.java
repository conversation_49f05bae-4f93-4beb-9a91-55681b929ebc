package com.shouqianba.trade.fund.settlement.infrastructure.repository.domain;

import com.shouqianba.trade.fund.settlement.common.exception.FundSettlementBizException;
import com.shouqianba.trade.fund.settlement.common.exception.enums.FundSettlementRespCodeEnum;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.datasource.mysql.settlementstatistics.SettlementStatisticsDao;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.datasource.mysql.settlementstatistics.query.SettlementStatisticsQuery;
import com.shouqianba.trade.fund.settlement.infrastructure.repository.domain.converter.SettlementStatisticsConverter;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.SettlementStatisticsAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.query.SettlementStatisticsAggrQuery;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.repository.SettlementStatisticsDomainRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 结算统计仓储实现
 *
 * <AUTHOR> Date: 2025/07/15 Time: 10:34 AM
 */
@Repository
public class SettlementStatisticsRepositoryImpl implements SettlementStatisticsDomainRepository {

    @Resource
    private SettlementStatisticsDao settlementStatisticsDao;
    @Resource
    private SettlementStatisticsConverter converter;

    @Override
    public void save(SettlementStatisticsAggrRoot aggrRoot) {
        if (aggrRoot.isNeedAdd()) {
            settlementStatisticsDao.insert(converter.toSettlementStatisticsPO(aggrRoot));
        } else if (aggrRoot.isNeedModify()) {
            int affectRow = settlementStatisticsDao.update(converter.toSettlementStatisticsPO(aggrRoot));
            if (affectRow <= 0) {
                throw new FundSettlementBizException(FundSettlementRespCodeEnum.CONCURRENT_MODIFY_ERROR);
            }
        }
    }

    @Override
    public SettlementStatisticsAggrRoot query(SettlementStatisticsAggrQuery aggrQuery) {
        return converter.toSettlementStatisticsAggrRoot(settlementStatisticsDao.select(SettlementStatisticsQuery.genSettlementStatisticsQuery(aggrQuery)));
    }

    @Override
    public List<SettlementStatisticsAggrRoot> batchQuery(SettlementStatisticsAggrQuery aggrQuery) {
        return converter.toSettlementStatisticsAggrRootList(settlementStatisticsDao.batchSelect(SettlementStatisticsQuery
                .genSettlementStatisticsQuery(aggrQuery)));
    }
}