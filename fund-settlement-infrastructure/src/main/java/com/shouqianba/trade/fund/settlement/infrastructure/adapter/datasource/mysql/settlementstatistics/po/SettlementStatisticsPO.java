package com.shouqianba.trade.fund.settlement.infrastructure.adapter.datasource.mysql.settlementstatistics.po;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 结算统计PO
 *
 * <AUTHOR> Date: 2025/07/15 Time: 10:34 AM
 */
@Data
@Accessors(chain = true)
public class SettlementStatisticsPO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 统计类型：1：商户维度清分统计，2：商户维度分账统计
     */
    private Byte type;

    /**
     * 收款方主体类型：1：品牌，2：商户，3：门店
     */
    private Byte payeeType;

    /**
     * 品牌编号
     */
    private String brandSn;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 门店编号
     */
    private String storeSn;

    /**
     * 收款方信息
     */
    private JsonNode payeeInfo;

    /**
     * 收单机构
     */
    private Short acquiringCompany;

    /**
     * 统计日期
     */
    private LocalDate statisticsDate;

    /**
     * 金额信息
     */
    private JsonNode amount;

    /**
     * 领域信息
     */
    private JsonNode bizDomain;

    /**
     * 扩展字段
     */
    private JsonNode ext;

    /**
     * 创建时间
     */
    private LocalDateTime ctime;

    /**
     * 修改时间
     */
    private LocalDateTime mtime;

    /**
     * 版本号
     */
    private Long version;
}