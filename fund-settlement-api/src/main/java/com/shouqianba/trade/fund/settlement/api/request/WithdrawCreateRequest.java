package com.shouqianba.trade.fund.settlement.api.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;

/***
 * <AUTHOR> Date: 2025/6/15 Time: 10:20 AM
 */
@Getter
@ToString
@SuperBuilder(toBuilder = true)
@Jacksonized
public class WithdrawCreateRequest extends BaseRequest {

    /**
     * 客户端流水号，代表唯一键
     * 使用com.shouqianba.trade.fund.settlement.infrastructure.support.DefaultSerialGenerator进行发号
     */
    @JsonProperty("client_sn")
    @NotNull(message = "客户端流水号不能为空")
    private String clientSn;

    /**
     * 提现金额（单位：分）
     * 当商户提现指定金额时上传
     */
    private Long amount;

    /**
     * 是否提现所有金额
     * 当商户提现所有时上传
     */
    @JsonProperty("withdraw_all_amount")
    private Boolean withdrawAllAmount;

    /**
     * 提现的商户ID
     */
    @JsonProperty("merchant_id")
    @NotNull(message = "商户ID不能为空")
    private String merchantId;
}