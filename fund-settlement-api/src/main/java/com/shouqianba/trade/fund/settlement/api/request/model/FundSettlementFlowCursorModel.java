package com.shouqianba.trade.fund.settlement.api.request.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> Date: 2025/6/17 Time: 16:20 PM
 */
@Getter
@Builder
@ToString
@Jacksonized
public class FundSettlementFlowCursorModel {

    @NotNull(message = "游标字段不能为空")
    private FundBillCursorFieldEnum cursorField;

    private String endCursor;

    @NotNull(message = "查询数量不能为空")
    @Min(value = 1, message = "查询数量格式错误")
    @Max(value = 1000, message = "查询数量格式错误")
    private Integer count;

    @Getter
    public enum FundBillCursorFieldEnum {
        ID("id", "id", "资金池ID"),
        ;

        private final String outerField;
        private final String innerField;
        private final String desc;

        FundBillCursorFieldEnum(String outerField, String innerField, String desc) {
            this.outerField = outerField;
            this.innerField = innerField;
            this.desc = desc;
        }

        @JsonCreator
        public static FundBillCursorFieldEnum ofField(String field) {
            FundBillCursorFieldEnum[] fields = FundBillCursorFieldEnum.values();
            for (FundBillCursorFieldEnum cursorField : fields) {
                if (cursorField.getOuterField().equalsIgnoreCase(field)) {
                    return cursorField;
                }
            }
            return null;
        }
    }
}