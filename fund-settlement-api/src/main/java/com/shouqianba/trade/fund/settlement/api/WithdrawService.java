package com.shouqianba.trade.fund.settlement.api;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.shouqianba.trade.fund.settlement.api.request.WithdrawBalanceQueryRequest;
import com.shouqianba.trade.fund.settlement.api.request.WithdrawCreateRequest;
import com.shouqianba.trade.fund.settlement.api.result.WithdrawBalanceQueryResult;
import com.shouqianba.trade.fund.settlement.api.result.WithdrawCreateResult;
import com.wosai.general.result.SingleResult;

/**
 * 提现服务接口
 *
 * <AUTHOR> Date: 2025/6/15 Time: 10:20 AM
 */
@JsonRpcService("/rpc/withdraw")
public interface WithdrawService {

    /**
     * 创建提现申请
     *
     * @param request 提现申请请求
     * @return 提现申请结果
     */
    SingleResult<WithdrawCreateResult> createWithdraw(WithdrawCreateRequest request);


    /**
     * 查询可提现金额
     *
     * @param request 查询可提现金额请求
     * @return 可提现金额结果
     */
    SingleResult<WithdrawBalanceQueryResult> queryWithdrawBalance(WithdrawBalanceQueryRequest request);

}