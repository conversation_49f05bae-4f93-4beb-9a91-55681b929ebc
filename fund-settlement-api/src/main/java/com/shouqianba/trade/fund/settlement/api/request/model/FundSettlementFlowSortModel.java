package com.shouqianba.trade.fund.settlement.api.request.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shouqianba.trade.fund.settlement.api.request.model.enums.SortEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/6/17 Time: 16:20 PM
 */
@Getter
@Builder
@ToString
@Jacksonized
public class FundSettlementFlowSortModel {

    /**
     * 排序
     */
    private SortEnum sort;

    /**
     * 排序字段
     */
    @NotNull(message = "排序字段不能为空")
    private FundSettlementFlowSortModel.FundSettlementFlowSortFieldEnum sortField;

    @JsonIgnore
    public boolean isDesc() {
        return Objects.equals(sort, SortEnum.DESC);
    }

    @Getter
    public enum FundSettlementFlowSortFieldEnum {
        ID("id", "id", "资金账单ID"),
        ;

        private final String outerField;
        private final String innerField;
        private final String desc;

        FundSettlementFlowSortFieldEnum(String outerField, String innerField, String desc) {
            this.outerField = outerField;
            this.innerField = innerField;
            this.desc = desc;
        }

        @JsonCreator
        public static FundSettlementFlowSortFieldEnum ofField(String field) {
            FundSettlementFlowSortFieldEnum[] fields = FundSettlementFlowSortFieldEnum.values();
            for (FundSettlementFlowSortFieldEnum sortField : fields) {
                if (sortField.getOuterField().equalsIgnoreCase(field)) {
                    return sortField;
                }
            }
            return null;
        }

    }

}
