package com.shouqianba.trade.fund.settlement.api.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotEmpty;

/**
 * 查询可提现金额请求
 *
 * <AUTHOR> Date: 2025/7/15 Time: 10:20 AM
 */
@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString
public class WithdrawBalanceQueryRequest extends BaseRequest {

    /**
     * 品牌编号
     */
    @NotEmpty(message = "品牌编号不能为空")
    private String brandSn;

    /**
     * 商户编号
     */
    @JsonProperty("merchant_sn")
    private String merchantSn;

    /**
     * 收单公司
     */
    private Integer acquiringCompany;

    /**
     * 统计时间
     */
    private String statisticsDate;
}