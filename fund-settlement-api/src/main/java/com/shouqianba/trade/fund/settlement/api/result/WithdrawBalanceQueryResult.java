package com.shouqianba.trade.fund.settlement.api.result;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

/**
 * 查询可提现金额结果
 *
 * <AUTHOR> Date: 2025/7/15 Time: 10:20 AM
 */
@Getter
@Builder
@Jacksonized
@ToString
public class WithdrawBalanceQueryResult {

    /**
     * 可提现金额（单位：分）
     */
    @JsonProperty("withdraw_balance")
    private Long withdrawBalance;


}