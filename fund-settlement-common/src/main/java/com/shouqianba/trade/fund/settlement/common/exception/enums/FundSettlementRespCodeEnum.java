package com.shouqianba.trade.fund.settlement.common.exception.enums;


import lombok.Getter;

/**
 * <AUTHOR> Date: 2022/12/14 Time: 3:18 PM
 */
@Getter
public enum FundSettlementRespCodeEnum implements RespCode {

    PROCESS_SUCCESS("0000", "处理成功"),
    ILLEGAL_ARGUMENT("4000", "入参错误"),
    SERVER_ERROR("9999", "系统错误"),

    // 事件相关 (51xx)
    EVENT_NOT_EXIST("5101", "事件不存在"),
    EVENT_STRATEGY_NOT_EXIST("5102", "事件处理策略不存在"),

    // 商户与品牌相关 (53xx)
    BRAND_NOT_EXIST("5301", "渠道配置不存在"),
    CHANNEL_CONFIG_NOT_EXIST("5302", "渠道配置不存在"),
    MERCHANT_NOT_EXIST("5331", "商户不存在"),
    STORE_NOT_EXIST("5341", "门店不存在"),

    // 任务相关 (54xx)
    TASK_RECORD_NOT_EXIST("5401", "任务记录不存在"),

    // 6xxx - 外部系统的异常

    // 商户品牌查询相关 (64xx)
    BRAND_DETAIL_QUERY_ERROR("6401", "品牌详情查询失败"),
    GET_BRAND_MERCHANT_INFO_ERROR("6402", "获取品牌商户信息失败"),
    PAGE_QUERY_BRAND_MERCHANT_ERROR("6403", "分页查询品牌商户失败"),
    QUERY_STORE_INFO_ERROR("6404", "查询门店信息失败"),

    // 9xxx - 系统内部的配置或流程异常
    // 配置相关 (91xx)
    CONFIG_NOT_EXIST("9101", "配置不存在"),
    CONFIG_BEHAVIOR_TREE_NOT_EXIST("9104", "行为树配置不存在"),
    CONFIG_BLACKBOARD_NOT_EXIST("9105", "行为树黑板不存在"),
    CONFIG_BEHAVIOR_TREE_CONFIG_INVALID("9106", "行为树配置无效"),
    CONFIG_BLACKBOARD_VALUE_INVALID("9107", "行为树黑板值无效"),
    BEHAVIOR_TREE_EXECUTION_FAILED("9108", "行为树执行失败"),
    CONFIG_BLACKBOARD_CONFIG_INVALID("9109", "行为树黑板配置无效"),

    EXTERNAL_SERVICE_ERROR("6000", "外部服务错误"),
    EXTERNAL_SERVICE_RESULT_NOT_EXIST("6001", "外部服务结果不存在"),

    ARGUMENT_ERROR("4xxx", "入参不合法"),
    INNER_BIZ_EXCEPTION("5xxx", "系统内部的业务异常"),
    EXTERNAL_SERVER_EXCEPTION("6xxx", "外部系统的异常"),
    INNER_CONFIG_LOGISTICS_EXCEPTION("9xxx", "系统内部的配置或流程异常"),

    APOLLO_CONFIG_NOT_EXIST("9000", "Apollo配置文件不存在"),
    UNSUPPORTED_OPERATION_EXCEPTION("9001", "不支持的操作"),
    PERMISSION_DENIED("9002", "操作权限不足"),
    CONCURRENT_MODIFY_ERROR("9005", "并发修改异常"),
    PROCESS_STRATEGY_NOT_EXIST("9007", "处理策略不存在"),
    SEQUENCE_GENERATOR_NOT_FOUND("9020", "序列生成器不存在"),
    MISSING_PRE_OPERATION("9021", "缺少前置操作"),
    MISSING_THREAD_POOL("9022", "线程池缺失"),
    BIZ_PARAMS_ILLEGAL("9023", "业务参数非法"),
    BIZ_PARAMS_MISSING("9024", "业务参数缺失"),
    BIZ_OBJECT_ILLEGAL("9025", "业务对象非法"),
    BIZ_OBJECT_MISSING("9026", "业务对象缺失"),
    ENTRY_BEHAVIOR_TREE_NOT_EXIST("9225", "入账行为树不存在,请尽快配置"),
    BLACKBOARD_ID_NOT_EXIST("9226", "黑板ID不存在"),
    FTP_DOWNLOAD_ERROR("5509", "品牌账单FTP下载失败"),
    PARSE_GENERIC_RECORD_ERROR("9203", "解析通用记录失败"),

    /**
     * 结算流水不存在
     */
    SETTLEMENT_FLOW_NOT_EXIST("9300", "结算流水不存在"),
    SETTLEMENT_FLOW_TYPE_ERROR( "9301", "结算流水类型错误"),
    ACCOUNT_TYPE_ERROR( "9302", "账户类型错误"),
    SETTLEMENT_BATCH_NOT_EXIST( "9303", "结算批次不存在"),
    SETTLEMENT_BATCH_TYPE_ERROR( "9304", "结算批次类型错误"),
    SETTLEMENT_BATCH_RELATED_POOL_NOT_EXIST("9305", "结算批次关联的结算池不存在"),
    SETTLEMENT_BATCH_NOT_FINISHED_CREATE("9306", "结算批次未完成创建"),
    SETTLEMENT_BATCH_FINISHED_CREATE("9307", "结算批次已完成创建,不允许再创建资金流"),

    SETTLEMENT_STATISTICS_NOT_EXIST("9401", "结算统计不存在"),

    OSS_CONTENT_NOT_FOUND("6060", "文件不存在");

    private final String code;
    private final String msg;

    FundSettlementRespCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }

    @Override
    public String getCode() {
        return code;
    }
}
