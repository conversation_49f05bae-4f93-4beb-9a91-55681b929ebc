package com.shouqianba.trade.fund.settlement.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.BaseIndependentEventStrategy;
import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.context.impl.SharingSettlementEventContext;
import com.shouqianba.trade.fund.settlement.common.exception.FundSettlementBizException;
import com.shouqianba.trade.fund.settlement.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.settlement.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.BrandSettleClient;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res.SettlementAdjustDetailResult;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res.SettlementTransferResult;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventContentBatchEntrySettlementVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventContentVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.SettlementFlowDomainRepository;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.SettlementFlowAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.SettlementStatusEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.SettlementStatisticsAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.repository.SettlementStatisticsDomainRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2025/6/14 Time: 16:20 PM
 */
@Slf4j
@Component
public class SharingSettlementStrategy extends BaseIndependentEventStrategy<SharingSettlementEventContext> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.SHARING_SETTLE;
    private static final String SUCCESS_RESULT = "转帐成功";
    private static final String PROCESSING_RESULT = "已发起转帐";
    private static final String PROCESSING = "转帐中";
    private static final String FAILED_RESULT = "转帐失败";
    private static final String FAILED_NOT_EXIST = "转帐不存在";

    @Resource
    private SettlementFlowDomainRepository settlementFlowDomainRepository;
    @Resource
    private BrandSettleClient brandSettleClient;
    @Resource
    private SettlementStatisticsDomainRepository settlementStatisticsDomainRepository;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<SharingSettlementEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }


    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(SharingSettlementEventContext context) {
        return new FundBillSharingRunnable(context);
    }

    public class FundBillSharingRunnable extends IndependentEventStrategyRunnable {
        private final SharingSettlementEventContext context;

        public FundBillSharingRunnable(SharingSettlementEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context, new ExternalInvokeWithTransactionTemplate<>() {

                @Override
                protected boolean preInvokeExternal(SharingSettlementEventContext context) {
                    return Boolean.TRUE;
                }

                @Override
                protected void invokeExternal(SharingSettlementEventContext context) throws Throwable {
                    EventAggrRoot eventAggrRoot = context.getEventAggrRoot();

                    SettlementFlowAggrRoot settlementFlowAggrRoot = settlementFlowDomainRepository.query(context.genSettlementFlowAggrQuery());
                    settlementFlowAggrRoot.checkExist();
                    context.bindSettlementFlowAggrRoot(settlementFlowAggrRoot);

                    SettlementStatisticsAggrRoot settlementStatisticsAggrRoot = SettlementStatisticsAggrRoot.newEmptyInstance();
                    if (settlementFlowAggrRoot.isPending()) {
                        SettlementTransferResult result = brandSettleClient.transfer(context.genTransferRequest());
                        if (result.isExists()) {
                            eventAggrRoot.updateResult(PROCESSING_RESULT);
                            eventAggrRoot.updateContent(EventContentVO.builder()
                                    .bizContent(EventContentBatchEntrySettlementVO.builder()
                                            .adjustSn(result.getSn())
                                            .build()
                                            .toJsonString())
                                    .build());
                            settlementFlowAggrRoot.updateStatus(SettlementStatusEnum.PROCESSING);
                        }
                    } else if (settlementFlowAggrRoot.isProcessing()) {
                        SettlementAdjustDetailResult queryResult = brandSettleClient
                                .getAdjustDetail(context.genSettlementAdjustDetailQueryRequest());
                        if (queryResult.isExists()) {
                            if (queryResult.isSuccess()) {
                                eventAggrRoot.processSuccess(SUCCESS_RESULT);
                                settlementStatisticsAggrRoot = settlementStatisticsDomainRepository
                                        .query(context.genSettlementStatisticsAggrQuery());
                                settlementStatisticsAggrRoot.addAmount(settlementFlowAggrRoot.getAmount().getOriginAmount());
                                settlementFlowAggrRoot.updateStatus(SettlementStatusEnum.SUCCESS);
                            } else if (queryResult.isFailed()) {
                                eventAggrRoot.processSuccess(FAILED_RESULT);
                                settlementFlowAggrRoot.updateStatus(SettlementStatusEnum.FAILED);
                            } else if (queryResult.isProcessing()) {
                                eventAggrRoot.processFailure(PROCESSING);
                            }
                        } else {
                            eventAggrRoot.processFailure(FAILED_NOT_EXIST);
                        }
                    } else {
                        eventAggrRoot.processSuccess(settlementFlowAggrRoot.isSuccess() ? SUCCESS_RESULT : FAILED_RESULT);
                    }
                    SettlementStatisticsAggrRoot finalSettlementStatisticsAggrRoot = settlementStatisticsAggrRoot;
                    transactionTemplate.executeWithoutResult(status -> {
                        settlementStatisticsDomainRepository.save(finalSettlementStatisticsAggrRoot);
                        settlementFlowDomainRepository.save(settlementFlowAggrRoot);
                        eventDomainRepository.save(eventAggrRoot);
                    });

                }

                @Override
                protected void postInvokeExternal(SharingSettlementEventContext SharingSettlementEventContext) {
                }

                @Override
                protected void onBizFailure(SharingSettlementEventContext context, FundSettlementBizException e) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    String result = e.getCode() + ":" + e.getMsg();
                    log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                            + result + ", 异常栈: ", e);
                    aggrRoot.processFailure("业务异常: " + result);
                }

                @Override
                protected void onFailure(SharingSettlementEventContext context, Throwable throwable) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ", throwable);
                    aggrRoot.processFailure("未知异常");
                }

                @Override
                protected void doFinally(SharingSettlementEventContext context) {
                    secureUpdateEvent(context);
                }
            });
        }
    }

}