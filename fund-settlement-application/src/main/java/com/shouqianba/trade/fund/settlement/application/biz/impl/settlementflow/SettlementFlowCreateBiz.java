package com.shouqianba.trade.fund.settlement.application.biz.impl.settlementflow;

import com.shouqianba.trade.fund.settlement.api.request.model.FundSettlementAccountModel;
import com.shouqianba.trade.fund.settlement.application.biz.BaseBiz;
import com.shouqianba.trade.fund.settlement.application.biz.context.impl.settlementflow.SettlementFlowCreateContext;
import com.shouqianba.trade.fund.settlement.application.support.SettlementAccountSupportService;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.EventDomainRepository;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.SettlementBatchDomainRepository;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.SettlementBatchAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.SettlementFlowDomainRepository;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.SettlementFlowAggrRoot;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.List;


/***
 * <AUTHOR> Date: 2025/5/19 Time: 4:34 PM
 */
@Service
@Slf4j
public class SettlementFlowCreateBiz extends BaseBiz<SettlementFlowCreateContext> {
    @Resource
    private SettlementFlowDomainRepository settlementFlowDomainRepository;
    @Resource
    private EventDomainRepository eventDomainRepository;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private SettlementAccountSupportService accountInfoProcessSupportService;
    @Resource
    private SettlementBatchDomainRepository settlementBatchDomainRepository;

    @Override
    protected void doBiz(SettlementFlowCreateContext context) {

        // 处理收款账户信息
        processAccountInfo(context
                .getRequest()
                .getFromInfo(), context, true);

        // 处理付款账户信息
        processAccountInfo(context
                .getRequest()
                .getToInfo(), context, false);

        SettlementBatchAggrRoot settlementBatchAggrRoot =
                settlementBatchDomainRepository.query(context.genSettlementBatchAggrQuery());
        settlementBatchAggrRoot.checkExist();
        //todo
        //settlementBatchAggrRoot.checkCreateFinished();
        context.bindSettlementBatchAggrRoot(settlementBatchAggrRoot);

        // 保存结算流水
        SettlementFlowAggrRoot settlementFlowAggrRoot = context.genSettlementFlowAggrRoot();
        context.bindSettlementFlowAggrRoot(settlementFlowAggrRoot);
        try {
            transactionTemplate.executeWithoutResult(status -> {
                settlementFlowDomainRepository.save(settlementFlowAggrRoot);
                eventDomainRepository.save(context.genEventAggrRoot());
            });
        } catch (DuplicateKeyException ignore) {
            List<SettlementFlowAggrRoot> fundBillAggrRoots = settlementFlowDomainRepository.batchQuery(context.genSettlementFlowAggrQuery());
            context.bindSettlementFlowAggrRoot(fundBillAggrRoots.getFirst());
        }
    }

    private void processAccountInfo(
            FundSettlementAccountModel accountInfo,
            SettlementFlowCreateContext context,
            boolean isFrom) {
        SettlementAccountSupportService.AccountInfoResult result =
                accountInfoProcessSupportService.processAccountInfo(accountInfo);
        context.bindAccountInfo(isFrom, result.getBrandDetailInfo(),
                result.getMerchantInfo(), result.getStoreInfo());
    }
}
