package com.shouqianba.trade.fund.settlement.application.adapter.rest.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;

import com.shouqianba.trade.fund.settlement.api.request.BaseRequest;
import com.shouqianba.trade.fund.settlement.common.util.JsonUtils;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString
@SuperBuilder(toBuilder = true)
@Jacksonized
public class FundChannelNotifyRequest extends BaseRequest {

    public static final int NOTIFY_TYPE_FUND_CHANNEL_NOTIFY = 1;
    public static final int NOTIFY_TYPE_CLEARING_NOTIFY = 2;

    @JsonProperty("notify_type")
    private Integer notifyType; // 1-清分结果通知

    @JsonProperty("notify_data")
    private Object notifyData;

    public <T> T genTypeValue(Class<T> tClass) {
        return JsonUtils.convertToObject(notifyData, tClass);
    }

    public <T> T genTypeValue(TypeReference<T> reference) {
        return JsonUtils.convertToObject(notifyData, reference);
    }

}