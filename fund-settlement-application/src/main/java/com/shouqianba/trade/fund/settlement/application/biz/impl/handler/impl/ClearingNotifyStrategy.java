package com.shouqianba.trade.fund.settlement.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.BaseIndependentEventStrategy;
import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.context.impl.ClearingNotifyEventContext;
import com.shouqianba.trade.fund.settlement.common.exception.FundSettlementBizException;
import com.shouqianba.trade.fund.settlement.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.settlement.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.BrandSettleClient;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.SettlementBatchDomainRepository;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.SettlementBatchAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.enums.SettlementBatchStatusEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.SettlementFlowDomainRepository;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.repository.SettlementStatisticsDomainRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2025/6/14 Time: 16:20 PM
 */
@Slf4j
@Component
public class ClearingNotifyStrategy extends BaseIndependentEventStrategy<ClearingNotifyEventContext> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.CLEARING_NOTIFY;
    private static final String SUCCESS_RESULT = "清分成功";
    private static final String SUCCESS_FAILURE = "清分失败";


    @Resource
    private SettlementFlowDomainRepository settlementFlowDomainRepository;
    @Resource
    private BrandSettleClient brandSettleClient;
    @Resource
    private SettlementStatisticsDomainRepository settlementStatisticsDomainRepository;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private SettlementBatchDomainRepository fundBatchDomainRepository;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<ClearingNotifyEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }


    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(
            ClearingNotifyEventContext context) {
        return new FundBillSharingRunnable(context);
    }

    public class FundBillSharingRunnable extends IndependentEventStrategyRunnable {
        private final ClearingNotifyEventContext context;

        public FundBillSharingRunnable(ClearingNotifyEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context, new ExternalInvokeWithTransactionTemplate<>() {

                @Override
                protected boolean preInvokeExternal(ClearingNotifyEventContext context) {
                    return Boolean.TRUE;
                }

                @Override
                protected void invokeExternal(ClearingNotifyEventContext context) throws Throwable {
                    EventAggrRoot eventAggrRoot = context.getEventAggrRoot();

                    SettlementBatchAggrRoot fundBatchAggrRoot =
                            fundBatchDomainRepository.query(context.genSettlementBatchAggrQuery()); // todo 调整字段
                    fundBatchAggrRoot.checkExist();
                    context.bindSettlementBatchAggrRoot(fundBatchAggrRoot);
                    if(context.isClearingSuccess()){
                        fundBatchAggrRoot.updateStatus(SettlementBatchStatusEnum.SUCCESS);
                        // todo 调用清分
                        eventAggrRoot.processSuccess(SUCCESS_RESULT);
                        return;
                    }
                    eventAggrRoot.processFailure(SUCCESS_FAILURE);
                }

                @Override
                protected void postInvokeExternal(ClearingNotifyEventContext ClearingNotifyEventContext) {
                }

                @Override
                protected void onBizFailure(ClearingNotifyEventContext context, FundSettlementBizException e) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    String result = e.getCode() + ":" + e.getMsg();
                    log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                            + result + ", 异常栈: ", e);
                    aggrRoot.processFailure("业务异常: " + result);
                }

                @Override
                protected void onFailure(ClearingNotifyEventContext context, Throwable throwable) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ", throwable);
                    aggrRoot.processFailure("未知异常");
                }

                @Override
                protected void doFinally(ClearingNotifyEventContext context) {
                    secureUpdateEvent(context);
                }
            });
        }
    }

}