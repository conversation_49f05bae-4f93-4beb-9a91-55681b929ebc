package com.shouqianba.trade.fund.settlement.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.BaseIndependentEventStrategy;
import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.context.impl.FundChannelNotifyEventContext;
import com.shouqianba.trade.fund.settlement.common.exception.FundSettlementBizException;
import com.shouqianba.trade.fund.settlement.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.settlement.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.BrandSettleClient;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.SettlementBatchDomainRepository;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.repository.SettlementStatisticsDomainRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2025/6/14 Time: 16:20 PM
 */
@Slf4j
@Component
public class FundChannelNotifyStrategy extends BaseIndependentEventStrategy<FundChannelNotifyEventContext> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.FUND_CHANNEL_NOTIFY;
    private static final String SUCCESS_RESULT = "转帐成功";
    private static final String PROCESSING_RESULT = "已发起转帐";
    private static final String PROCESSING = "转帐中";
    private static final String FAILED_RESULT = "转帐失败";
    private static final String FAILED_NOT_EXIST = "转帐不存在";

    @Resource
    private SettlementBatchDomainRepository fundBatchDomainRepository;
    @Resource
    private BrandSettleClient brandSettleClient;
    @Resource
    private SettlementStatisticsDomainRepository settlementStatisticsDomainRepository;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<FundChannelNotifyEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }


    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(
            FundChannelNotifyEventContext context) {
        return new FundBillSharingRunnable(context);
    }

    public class FundBillSharingRunnable extends IndependentEventStrategyRunnable {
        private final FundChannelNotifyEventContext context;

        public FundBillSharingRunnable(FundChannelNotifyEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context,
                    new ExternalInvokeWithTransactionTemplate<>() {

                        @Override
                        protected boolean preInvokeExternal(FundChannelNotifyEventContext context) {
                            return Boolean.TRUE;
                        }

                        @Override
                        protected void invokeExternal(FundChannelNotifyEventContext context) throws Throwable {
                            EventAggrRoot eventAggrRoot = context.getEventAggrRoot();

                            // todo 调用清分

                        }

                        @Override
                        protected void postInvokeExternal(FundChannelNotifyEventContext FundChannelNotifyEventContext) {
                        }

                        @Override
                        protected void onBizFailure(
                                FundChannelNotifyEventContext context, FundSettlementBizException e) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            String result = e.getCode() + ":" + e.getMsg();
                            log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                                    + result + ", 异常栈: ", e);
                            aggrRoot.processFailure("业务异常: " + result);
                        }

                        @Override
                        protected void onFailure(FundChannelNotifyEventContext context, Throwable throwable) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ",
                                    throwable);
                            aggrRoot.processFailure("未知异常");
                        }

                        @Override
                        protected void doFinally(FundChannelNotifyEventContext context) {
                            secureUpdateEvent(context);
                        }
                    });
        }
    }

}