package com.shouqianba.trade.fund.settlement.application.biz.impl.withdraw;

import com.shouqianba.trade.fund.settlement.application.biz.BaseBiz;
import com.shouqianba.trade.fund.settlement.application.biz.context.impl.withdraw.WithdrawBalanceQueryContext;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.BrandSettleClient;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res.SettlementAccountWalletResult;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.SettlementStatisticsAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.repository.SettlementStatisticsDomainRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 查询可提现金额业务实现
 *
 * <AUTHOR> Date: 2025/7/15 Time: 10:20 AM
 */
@Slf4j
@Component
public class WithdrawBalanceQueryBiz extends BaseBiz<WithdrawBalanceQueryContext> {

    @Resource
    private BrandSettleClient brandSettleClient;

    @Resource
    private SettlementStatisticsDomainRepository settlementStatisticsDomainRepository;

    @Override
    protected void doBiz(WithdrawBalanceQueryContext context) {
        // 查询清分统计记录
        List<SettlementStatisticsAggrRoot> clearingStatistics =
                settlementStatisticsDomainRepository.batchQuery(context.genClearingStatisticsQuery());
        context.bindClearingStatistics(clearingStatistics);

        // 查询分账统计记录
        List<SettlementStatisticsAggrRoot> splitStatistics =
                settlementStatisticsDomainRepository.batchQuery(context.genSplitStatisticsQuery());
        context.bindSplitStatistics(splitStatistics);

        // 计算清分统计金额
        Long clearingAmount = calculateTotalAmount(clearingStatistics);
        context.bindClearingAmount(clearingAmount);

        // 计算分账统计金额
        Long splitAmount = calculateTotalAmount(splitStatistics);
        context.bindSplitAmount(splitAmount);

        // 查询账户余额
        SettlementAccountWalletResult accountWalletResult =
                brandSettleClient.getWallet(context.genAccountWalletQueryRequest());
        context.bindAccountWalletResult(accountWalletResult);

        // 计算可提现金额
        Long withdrawBalance = calculateWithdrawBalance(clearingAmount, splitAmount, accountWalletResult);
        context.bindWithdrawBalance(withdrawBalance);
    }

    /**
     * 计算统计记录总金额
     *
     * @param statisticsList 统计记录列表
     * @return 总金额
     */
    private Long calculateTotalAmount(List<SettlementStatisticsAggrRoot> statisticsList) {
        if (CollectionUtils.isEmpty(statisticsList)) {
            return 0L;
        }

        return statisticsList.stream()
                .filter(Objects::nonNull)
                .filter(stat -> Objects.nonNull(stat.getAmount()))
                .mapToLong(stat -> stat.getAmount().getTotalAmount())
                .sum();
    }

    /**
     * 计算可提现金额
     *
     * @param clearingAmount      清分统计金额
     * @param splitAmount         分账统计金额
     * @param accountWalletResult 账户余额结果
     * @return 可提现金额
     */
    private Long calculateWithdrawBalance(Long clearingAmount, Long splitAmount,
                                          SettlementAccountWalletResult accountWalletResult) {
        // 判断清分统计是否等于分账统计
        boolean isEqual = Objects.equals(clearingAmount, splitAmount);

        // 获取账户可提现余额
        Long accountWithdrawBalance = accountWalletResult.getWithdrawBalance();
        if (accountWithdrawBalance == null) {
            accountWithdrawBalance = 0L;
        }

        // 如果清分统计等于分账统计，返回全部可提现金额
        if (isEqual) {
            return accountWithdrawBalance;
        }

        // 否则返回可提现余额减去分账统计记录的金额
        Long result = accountWithdrawBalance - splitAmount;
        return Math.max(result, 0L); // 确保不返回负数
    }
}