package com.shouqianba.trade.fund.settlement.application.biz.impl.notify;

import com.shouqianba.trade.fund.settlement.application.biz.BaseBiz;
import com.shouqianba.trade.fund.settlement.application.biz.context.impl.notify.FundNotifyContext;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.EventDomainRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2025/05/23 Time: 11:16 AM
 */
@Slf4j
@Service
public class FundNotifyBiz extends BaseBiz<FundNotifyContext> {

    @Resource
    private EventDomainRepository eventDomainRepository;

    @Override
    protected void doBiz(FundNotifyContext context) {
        log.info("[资金通道通知]>>>>>>接收到通知: {}", context.getRequest());
        if(context.isFundChannelNotify()){
            eventDomainRepository.save(context.genFundChannelNotifyEventAggrRoot());
        }else if(context.isClearingNotify()) {
            eventDomainRepository.save(context.genClearingNotifyEventAggrRoot());
        }
        log.info("[资金通道通知]>>>>>>处理完成");
    }
}