package com.shouqianba.trade.fund.settlement.application.biz.context.impl.settlementflow;

import com.shouqianba.trade.fund.settlement.api.request.SettlementFlowCreateRequest;
import com.shouqianba.trade.fund.settlement.api.request.model.FundSettlementAccountModel;
import com.shouqianba.trade.fund.settlement.api.result.SettlementFlowCreateResult;
import com.shouqianba.trade.fund.settlement.application.biz.context.BaseContext;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandbusiness.model.res.BrandDetailInfoGetResult;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandbusiness.model.res.BrandMerchantInfoQueryResult;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.coreb.model.res.StoreInfoQueryResult;
import com.shouqianba.trade.fund.settlement.infrastructure.support.DefaultSerialGenerator;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.EventAggrRootFactory;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventContentSettlementFlowCreateVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventContentVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventExtVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.SettlementBatchAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.query.SettlementBatchAggrQuery;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.SettlementFlowAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.SettlementFlowAggrRootFactory;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.AccountTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.FlowTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.SettlementStatusEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.query.SettlementFlowAggrQuery;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowAccountInfoVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowAmountVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowBizInfoVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowTradeInfoVO;
import lombok.Getter;

import java.util.Objects;
import java.util.Optional;

/***
 * <AUTHOR> Date: 2025/5/19 Time: 4:34 PM
 */
@Getter
public class SettlementFlowCreateContext extends BaseContext {

    private final SettlementFlowCreateRequest request;
    private final AccountInfo fromAccount = new AccountInfo();
    private final AccountInfo toAccount = new AccountInfo();

    private SettlementBatchAggrRoot settlementBatchAggrRoot;
    private SettlementFlowAggrRoot settlementFlowAggrRoot;

    public SettlementFlowAggrQuery genSettlementFlowAggrQuery() {
        return SettlementFlowAggrQuery
                .builder()
                .type(request.getType())
                .transSn(request.getTransSn())
                .orderSn(request.getOrderSn())
                .build();
    }


    @Getter
    private static class AccountInfo {
        private BrandDetailInfoGetResult brandDetailInfo;
        private BrandMerchantInfoQueryResult brandMerchantInfo;
        private StoreInfoQueryResult storeInfo;
    }

    private SettlementFlowCreateContext(SettlementFlowCreateRequest request) {
        this.request = request;
    }

    public static SettlementFlowCreateContext newInstance(SettlementFlowCreateRequest request) {
        return new SettlementFlowCreateContext(request);
    }

    public SettlementBatchAggrQuery genSettlementBatchAggrQuery() {
        return SettlementBatchAggrQuery
                .builder()
                .id(request.getBatchId())
                .build();
    }

    public void bindSettlementBatchAggrRoot(SettlementBatchAggrRoot settlementBatchAggrRoot) {
        this.settlementBatchAggrRoot = settlementBatchAggrRoot;
    }


    public void bindAccountInfo(
            boolean isFrom, BrandDetailInfoGetResult brandDetailInfo,
            BrandMerchantInfoQueryResult brandMerchantInfo, StoreInfoQueryResult storeInfo) {
        AccountInfo targetAccount = isFrom ? fromAccount : toAccount;
        targetAccount.brandDetailInfo = brandDetailInfo;
        targetAccount.brandMerchantInfo = brandMerchantInfo;
        targetAccount.storeInfo = storeInfo;
    }

    public SettlementFlowAggrRoot genSettlementFlowAggrRoot() {
        FundSettlementAccountModel fromInfo = request.getFromInfo();
        FundSettlementAccountModel toInfo = request.getToInfo();

        return SettlementFlowAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genFundSettlementFlowId())
                .type(FlowTypeEnum.ofCode(request.getType()))
                .transSn(request.getTransSn())
                .orderSn(request.getOrderSn())
                .batchId(request.getBatchId())
                .poolId(request.getPoolId())
                .fromType(AccountTypeEnum.ofCode(fromInfo.getType()))
                .fromBrandSn(fromInfo.getBrandSn())
                .fromMerchantSn(fromInfo.getMerchantSn())
                .fromStoreSn(fromInfo.getStoreSn())
                .fromInfo(buildAccountInfo(fromInfo, fromAccount))
                .toType(AccountTypeEnum.ofCode(toInfo.getType()))
                .toBrandSn(toInfo.getBrandSn())
                .toMerchantSn(toInfo.getMerchantSn())
                .toStoreSn(toInfo.getStoreSn())
                .toInfo(buildAccountInfo(toInfo, toAccount))
                .amount(SettlementFlowAmountVO.genFromJsonObject(request.getAmount(), SettlementFlowAmountVO.class))
                .tradeInfo(SettlementFlowTradeInfoVO.genFromJsonObject(request.getTradeInfo(),
                        SettlementFlowTradeInfoVO.class))
                .bizInfo(SettlementFlowBizInfoVO.genFromJsonObject(request.getBizInfo(), SettlementFlowBizInfoVO.class))
                .status(SettlementStatusEnum.PENDING)
                .build();
    }

    private SettlementFlowAccountInfoVO buildAccountInfo(FundSettlementAccountModel info, AccountInfo accountInfo) {
        return SettlementFlowAccountInfoVO
                .builder()
                .brandId(accountInfo
                        .getBrandMerchantInfo()
                        .getBrandId())
                .brandSn(accountInfo
                        .getBrandMerchantInfo()
                        .getBrandSn())
                .storeId(Optional
                        .ofNullable(accountInfo.getStoreInfo())
                        .map(StoreInfoQueryResult::getStoreId)
                        .orElse(null))
                .storeSn(Optional
                        .ofNullable(accountInfo.getStoreInfo())
                        .map(StoreInfoQueryResult::getStoreSn)
                        .orElse(null))
                .fundMerchantId(accountInfo
                        .getBrandMerchantInfo()
                        .getMerchantId())
                .fundMerchantSn(accountInfo
                        .getBrandDetailInfo()
                        .getMerchantSn())
                .sqbMerchantId(Optional
                        .ofNullable(accountInfo.getStoreInfo())
                        .map(StoreInfoQueryResult::getMerchantId)
                        .orElse(null))
                .sqbMerchantSn(Optional
                        .ofNullable(accountInfo.getStoreInfo())
                        .map(StoreInfoQueryResult::getMerchantSn)
                        .orElse(null))
                .channelMerchantSn(info.getChannelMerchantSn())
                .build();
    }

    public void bindSettlementFlowAggrRoot(SettlementFlowAggrRoot root) {
        this.settlementFlowAggrRoot = root;
    }

    public EventAggrRoot genEventAggrRoot() {
        if (Objects.equals(settlementFlowAggrRoot.getType(), FlowTypeEnum.ENTRY_SETTLEMENT)) {
            return EventAggrRoot.newEmptyInstance();
        }
        return EventAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator.getInstance().genEventId())
                .type(EventTypeEnum.SHARING_SETTLE)
                .associatedSn(settlementFlowAggrRoot.getIdStr())
                .optionalBuilder()
                .content(EventContentVO.builder()
                        .bizContent(EventContentSettlementFlowCreateVO.builder()
                                .build()
                                .toJsonString())
                        .build())
                .ext(EventExtVO.builder()
                        .mqKey(String.valueOf(settlementFlowAggrRoot.getBatchId()))
                        .build())
                .build();
    }

    public SettlementFlowCreateResult genResult() {
        return SettlementFlowCreateResult
                .builder()
                .settlementFlowSn(settlementFlowAggrRoot.getId())
                .build();
    }
}