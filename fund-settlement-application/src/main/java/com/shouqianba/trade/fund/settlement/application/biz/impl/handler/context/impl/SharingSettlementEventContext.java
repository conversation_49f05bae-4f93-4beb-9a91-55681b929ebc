package com.shouqianba.trade.fund.settlement.application.biz.impl.handler.context.impl;

import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.context.EventStrategyContext;
import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.holder.EventHandlerContext;
import com.shouqianba.trade.fund.settlement.common.util.JsonUtils;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req.SettlementAdjustDetailQueryRequest;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req.SettlementClearingDetailRequest;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req.SettlementClearingSubmitRequest;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req.SettlementTransferRequest;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventContentBatchEntrySettlementVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.SettlementBatchAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.query.SettlementBatchAggrQuery;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.SettlementFlowAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.query.SettlementFlowAggrQuery;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.query.SettlementStatisticsAggrQuery;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/3/17 Time: 16:20 PM
 */
@Getter
public class SharingSettlementEventContext extends EventStrategyContext<EventContentBatchEntrySettlementVO> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.SHARING_SETTLE;

    private EventContentBatchEntrySettlementVO eventContent;
    private SettlementBatchAggrRoot settlementBatchAggrRoot;
    private SettlementFlowAggrRoot settlementFlowAggrRoot;


    static {
        registerContext(EVENT_TYPE, new SharingSettlementEventContext());
    }

    private SharingSettlementEventContext() {
    }

    private SharingSettlementEventContext(EventAggrRoot eventAggrRoot) {
        super(eventAggrRoot);
    }

    @Override
    public EventStrategyContext<EventContentBatchEntrySettlementVO> rebuildContext(EventHandlerContext context) {
        return new SharingSettlementEventContext(context.getEventAggrRoot());
    }

    @Override
    public EventContentBatchEntrySettlementVO getBizContent() {
        if (Objects.isNull(eventContent)) {
            eventContent = JsonUtils.parseObject(getEventAggrRoot()
                            .getContent()
                            .getBizContent()
                    , EventContentBatchEntrySettlementVO.class);
        }
        return eventContent;
    }

    public SettlementClearingSubmitRequest genSettlementClearingSubmitRequest() {
        return SettlementClearingSubmitRequest
                .builder()
                .brandId(settlementBatchAggrRoot.getToBrandSn())      // e.g., "BRAND_001"
                .date(null)// todo
                .fundChannel(null) // todo
                .settlementChannel(null) // todo
                .clientSn(settlementBatchAggrRoot.getIdStr())
                .clearingFile(null) // todo
                .bucketName("<REPLACE_WITH_BUCKET_NAME>") // todo
                .build();
    }


    public SettlementBatchAggrQuery genSettlementBatchAggrQuery() {
        return SettlementBatchAggrQuery
                .builder()
                .id(getEventAggrRoot().getAssociatedSnNum())
                .build();
    }

    public void bindSettlementBatchAggrRoot(SettlementBatchAggrRoot settlementBatchAggrRoot) {
        this.settlementBatchAggrRoot = settlementBatchAggrRoot;
    }

    public SettlementClearingDetailRequest genSettlementClearingDetailRequest() {
        return SettlementClearingDetailRequest
                .builder()
                .clientSn(settlementBatchAggrRoot.getIdStr())
                .build();
    }

    public void bindSettlementFlowAggrRoot(SettlementFlowAggrRoot settlementFlowAggrRoot) {
        this.settlementFlowAggrRoot = settlementFlowAggrRoot;
    }

    public SettlementFlowAggrQuery genSettlementFlowAggrQuery() {
        return SettlementFlowAggrQuery
                .builder()
                .id(getEventAggrRoot().getAssociatedSnNum())
                .build();
    }

    public SettlementTransferRequest genTransferRequest() {
        return SettlementTransferRequest
                .builder()
                .clientSn(settlementFlowAggrRoot.getIdStr())
                .sourceMerchantSn(settlementFlowAggrRoot.getFromMerchantSn())
                .targetMerchantSn(settlementFlowAggrRoot.getToMerchantSn())
                .brandId(settlementFlowAggrRoot.getFromInfo().getBrandId())
                .amount(settlementFlowAggrRoot.getAmount().getSettleAmount())
                .build();
    }

    public SettlementAdjustDetailQueryRequest genSettlementAdjustDetailQueryRequest() {
        return SettlementAdjustDetailQueryRequest.builder()
                .clientSn(settlementFlowAggrRoot.getTransSn())
                .adjustSn(getBizContent().getAdjustSn())
                .brandId(settlementFlowAggrRoot.getFromInfo().getBrandId())
                .build();
    }

    public SettlementStatisticsAggrQuery genSettlementStatisticsAggrQuery() {
        return SettlementStatisticsAggrQuery.builder()
                .type(settlementFlowAggrRoot.getType().getCode())
                .brandSn(settlementFlowAggrRoot.getFromInfo().getBrandSn())
                .merchantSn(settlementFlowAggrRoot.getFromInfo().getSqbMerchantSn())
                .acquiringCompany(settlementFlowAggrRoot.getAcquiringCompany())
                .statisticsDate(settlementFlowAggrRoot.getSettleTimeLocalDate())
                .build();
    }
}
