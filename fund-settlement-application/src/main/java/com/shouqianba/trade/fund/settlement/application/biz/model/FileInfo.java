package com.shouqianba.trade.fund.settlement.application.biz.model;

import lombok.Builder;
import lombok.Getter;

import java.io.File;

/**
 * <AUTHOR> Date: 2025/6/17 Time: 16:20 PM
 */
@Getter
@Builder
public class FileInfo {
    
    /**
     * 文件名（不含路径，含扩展名）
     * 例如: batch_entry_settlement_123_20250617_162030.xlsx
     */
    private final String fileName;
    
    /**
     * 本地文件完整路径
     * 例如: /tmp/export/batch_entry_settlement_123_20250617_162030.xlsx
     */
    private final String localFilePath;
    
    /**
     * OSS文件路径（相对于bucket的路径）
     * 例如: optimus/prod/export/order/BRAND_001/batch_entry_settlement_123_20250617_162030.xlsx
     */
    private final String ossFilePath;
    
    /**
     * 文件内容（字节数组）
     */
    private final byte[] fileContent;
    
    /**
     * 获取本地文件对象
     */
    public File getLocalFile() {
        return new File(localFilePath);
    }
    
    /**
     * 检查本地文件是否存在
     */
    public boolean localFileExists() {
        return getLocalFile().exists();
    }
    
    /**
     * 获取文件大小（字节）
     */
    public long getFileSize() {
        if (fileContent != null) {
            return fileContent.length;
        }
        File file = getLocalFile();
        return file.exists() ? file.length() : 0;
    }
    
    /**
     * 从本地文件路径提取文件名
     */
    public static String extractFileName(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return null;
        }
        return new File(filePath).getName();
    }
}
