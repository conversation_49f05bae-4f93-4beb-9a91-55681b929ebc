package com.shouqianba.trade.fund.settlement.application.biz.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Date: 2025/6/17 Time: 16:20 PM
 */
@Data
@Component
@ConfigurationProperties(prefix = "fund.settlement.csv.export")
public class CsvExportConfig {

    /**
     * 批次大小，默认1000条
     */
    private int batchSize = 1000;

    /**
     * 文件输出目录
     */
    private String outputDir = "/tmp/settlement/csv";

    /**
     * 文件名前缀
     */
    private String fileNamePrefix = "settlement_flow";

    /**
     * 是否启用压缩
     */
    private boolean enableCompression = true;

    /**
     * 最大内存使用量（MB）
     */
    private int maxMemoryUsageMb = 100;

    /**
     * 写入超时时间（秒）
     */
    private int writeTimeoutSeconds = 300;
}
