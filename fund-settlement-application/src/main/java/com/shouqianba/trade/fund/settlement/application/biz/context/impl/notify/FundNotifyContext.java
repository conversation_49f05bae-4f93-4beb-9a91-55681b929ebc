package com.shouqianba.trade.fund.settlement.application.biz.context.impl.notify;

import com.fasterxml.jackson.core.type.TypeReference;
import com.shouqianba.trade.fund.settlement.application.adapter.rest.req.FundChannelNotifyRequest;
import com.shouqianba.trade.fund.settlement.application.adapter.rest.req.model.FundChannelNotifyDataModel;
import com.shouqianba.trade.fund.settlement.application.adapter.rest.req.model.FundClearingNotifyDataModel;
import com.shouqianba.trade.fund.settlement.application.biz.context.BaseContext;
import com.shouqianba.trade.fund.settlement.infrastructure.support.DefaultSerialGenerator;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.EventAggrRootFactory;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventContentClearingNotifyVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventContentFundChannelNotifyVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventContentVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventExtVO;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/05/23 Time: 11:16 AM
 */
@Getter
public class FundNotifyContext extends BaseContext {

    private final FundChannelNotifyRequest request;

    private FundNotifyContext(FundChannelNotifyRequest request) {
        this.request = request;
    }

    public static FundNotifyContext newInstance(FundChannelNotifyRequest request) {
        return new FundNotifyContext(request);
    }

    public boolean isFundChannelNotify() {
        return Objects.equals(request.getNotifyType(), FundChannelNotifyRequest.NOTIFY_TYPE_FUND_CHANNEL_NOTIFY);
    }

    public boolean isClearingNotify() {
        return Objects.equals(request.getNotifyType(), FundChannelNotifyRequest.NOTIFY_TYPE_CLEARING_NOTIFY);
    }

    public EventAggrRoot genFundChannelNotifyEventAggrRoot() {
        FundChannelNotifyDataModel fundChannelNotifyDataModel =
                request.genTypeValue(new TypeReference<>() {
                });
        return EventAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genEventId())
                .type(EventTypeEnum.FUND_CHANNEL_NOTIFY)
                .associatedSn(fundChannelNotifyDataModel.getActionId())
                .optionalBuilder()
                .content(EventContentVO
                        .builder()
                        .bizContent(EventContentFundChannelNotifyVO
                                .builder()
                                .brandId(fundChannelNotifyDataModel.getBrandId())
                                .actionId(fundChannelNotifyDataModel.getActionId())
                                .settlementChannel(fundChannelNotifyDataModel.getSettlementChannel())
                                .amount(fundChannelNotifyDataModel.getAmount())
                                .entryTime(fundChannelNotifyDataModel.getEntryTime())
                                .build()
                                .toJsonString())
                        .build())
                .ext(EventExtVO
                        .builder()
                        .mqKey(fundChannelNotifyDataModel.getActionId())
                        .build())
                .build();
    }

    public EventAggrRoot genClearingNotifyEventAggrRoot() {
        FundClearingNotifyDataModel fundClearingNotifyDataModel =
                request.genTypeValue(new TypeReference<>() {
                });
        return EventAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genEventId())
                .type(EventTypeEnum.CLEARING_NOTIFY)
                .associatedSn(fundClearingNotifyDataModel.getActionId())
                .optionalBuilder()
                .content(EventContentVO
                        .builder()
                        .bizContent(EventContentClearingNotifyVO
                                .builder()
                                .brandId(fundClearingNotifyDataModel.getBrandId())
                                .actionId(fundClearingNotifyDataModel.getActionId())
                                .clientSn(fundClearingNotifyDataModel.getClientSn())
                                .status(fundClearingNotifyDataModel.getStatus())
                                .finishTime(fundClearingNotifyDataModel.getFinishTime())
                                .failMsg(fundClearingNotifyDataModel.getFailMsg())
                                .clearingResultFile(fundClearingNotifyDataModel.getClearingResultFile())
                                .date(fundClearingNotifyDataModel.getDate())
                                .build()
                                .toJsonString())
                        .build())
                .ext(EventExtVO
                        .builder()
                        .mqKey(fundClearingNotifyDataModel.getActionId())
                        .build())
                .build();
    }

}