package com.shouqianba.trade.fund.settlement.application.biz.context.impl.withdraw;

import com.shouqianba.trade.fund.settlement.api.request.WithdrawBalanceQueryRequest;
import com.shouqianba.trade.fund.settlement.api.result.WithdrawBalanceQueryResult;
import com.shouqianba.trade.fund.settlement.application.biz.context.BaseContext;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req.SettlementAccountWalletQueryRequest;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res.SettlementAccountWalletResult;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.SettlementStatisticsAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.query.SettlementStatisticsAggrQuery;
import lombok.Getter;

import java.time.LocalDate;
import java.util.List;

/**
 * 查询可提现金额上下文
 *
 * <AUTHOR> Date: 2025/7/15 Time: 10:20 AM
 */
@Getter
public class WithdrawBalanceQueryContext extends BaseContext {

    private final WithdrawBalanceQueryRequest request;
    private SettlementAccountWalletResult accountWalletResult;
    private List<SettlementStatisticsAggrRoot> clearingStatistics;
    private List<SettlementStatisticsAggrRoot> splitStatistics;
    private Long clearingAmount;
    private Long splitAmount;
    private Long withdrawBalance;

    private WithdrawBalanceQueryContext(WithdrawBalanceQueryRequest request) {
        this.request = request;
    }

    public static WithdrawBalanceQueryContext newInstance(WithdrawBalanceQueryRequest request) {
        return new WithdrawBalanceQueryContext(request);
    }

    public SettlementAccountWalletQueryRequest genAccountWalletQueryRequest() {
        return SettlementAccountWalletQueryRequest.builder()
                .brandId(request.getBrandSn())
                .merchantSn(request.getMerchantSn())
                .build();
    }

    public SettlementStatisticsAggrQuery genClearingStatisticsQuery() {
        return SettlementStatisticsAggrQuery.builder()
                .type((byte) 1) // 清分统计类型
                .brandSn(request.getBrandSn())
                .merchantSn(request.getMerchantSn())
                .acquiringCompany(request.getAcquiringCompany())
                .statisticsDate(LocalDate.parse(request.getStatisticsDate()))
                .build();
    }

    public SettlementStatisticsAggrQuery genSplitStatisticsQuery() {
        return SettlementStatisticsAggrQuery.builder()
                .type((byte) 2) // 分账统计类型
                .brandSn(request.getBrandSn())
                .merchantSn(request.getMerchantSn())
                .acquiringCompany(request.getAcquiringCompany())
                .statisticsDate(LocalDate.parse(request.getStatisticsDate()))
                .build();
    }

    public void bindAccountWalletResult(SettlementAccountWalletResult accountWalletResult) {
        this.accountWalletResult = accountWalletResult;
    }

    public void bindClearingStatistics(List<SettlementStatisticsAggrRoot> clearingStatistics) {
        this.clearingStatistics = clearingStatistics;
    }

    public void bindSplitStatistics(List<SettlementStatisticsAggrRoot> splitStatistics) {
        this.splitStatistics = splitStatistics;
    }

    public void bindClearingAmount(Long clearingAmount) {
        this.clearingAmount = clearingAmount;
    }

    public void bindSplitAmount(Long splitAmount) {
        this.splitAmount = splitAmount;
    }

    public void bindWithdrawBalance(Long withdrawBalance) {
        this.withdrawBalance = withdrawBalance;
    }

    public WithdrawBalanceQueryResult genResult() {
        return WithdrawBalanceQueryResult.builder()
                .withdrawBalance(withdrawBalance)
                .build();
    }
}