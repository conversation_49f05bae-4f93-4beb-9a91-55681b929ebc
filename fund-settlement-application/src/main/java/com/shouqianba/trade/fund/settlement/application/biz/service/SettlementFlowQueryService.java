package com.shouqianba.trade.fund.settlement.application.biz.service;

import com.shouqianba.trade.fund.settlement.application.biz.config.CsvExportConfig;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.SettlementFlowDomainRepository;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.SettlementFlowAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.FlowTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.query.SettlementFlowAggrQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR> Date: 2025/6/17 Time: 16:20 PM
 */
@Slf4j
@Service
public class SettlementFlowQueryService {

    @Resource
    private SettlementFlowDomainRepository settlementFlowDomainRepository;
    
    @Resource
    private CsvExportConfig csvExportConfig;

    /**
     * 创建分页数据提供者
     * 
     * @param batchId 批次ID
     * @param flowType 流水类型
     * @return 数据提供者函数
     */
    public java.util.function.Function<Integer, List<SettlementFlowAggrRoot>> createDataProvider(
            Long batchId, FlowTypeEnum flowType) {
        
        // 使用AtomicReference来保存游标，确保线程安全
        AtomicReference<String> endCursorRef = new AtomicReference<>(null);
        
        return pageIndex -> {
            try {
                log.debug("[分页查询]开始查询第{}页数据，批次ID: {}, 流水类型: {}", 
                         pageIndex, batchId, flowType);
                
                SettlementFlowAggrQuery query = SettlementFlowAggrQuery.builder()
                        .type(flowType.getCode())
                        .batchId(batchId)
                        .querySize(csvExportConfig.getBatchSize())
                        .endCursor(endCursorRef.get())
                        .sortField("id") // 按ID排序
                        .isDesc(Boolean.FALSE) // 升序
                        .build();
                
                List<SettlementFlowAggrRoot> flowList = settlementFlowDomainRepository.batchQuery(query);
                
                if (CollectionUtils.isEmpty(flowList)) {
                    log.debug("[分页查询]第{}页无数据", pageIndex);
                    return Collections.emptyList();
                }
                
                // 更新游标为当前批次最后一条记录的ID
                String newEndCursor = flowList.getLast().getIdStr();
                endCursorRef.set(newEndCursor);
                
                log.debug("[分页查询]第{}页查询完成，返回{}条数据，新游标: {}", 
                         pageIndex, flowList.size(), newEndCursor);
                
                return flowList;
                
            } catch (Exception e) {
                log.error("[分页查询]第{}页查询失败，批次ID: {}, 流水类型: {}, 错误: {}", 
                         pageIndex, batchId, flowType, e.getMessage(), e);
                return Collections.emptyList();
            }
        };
    }
}
