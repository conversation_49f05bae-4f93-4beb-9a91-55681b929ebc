package com.shouqianba.trade.fund.settlement.application.biz.model.csv;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> Date: 2025/6/17 Time: 16:20 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class SettlementFlowCsvModel {

    /**
     * 交易流水号
     */
    @ExcelProperty(value = "tran_sn", index = 0)
    @ColumnWidth(30)
    private String tranSn;

    /**
     * 流水类型
     */
    @ExcelProperty(value = "type", index = 1)
    @ColumnWidth(20)
    private String type;
}
