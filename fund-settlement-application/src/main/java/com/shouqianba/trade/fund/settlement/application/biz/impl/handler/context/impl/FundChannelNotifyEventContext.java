package com.shouqianba.trade.fund.settlement.application.biz.impl.handler.context.impl;

import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.context.EventStrategyContext;
import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.holder.EventHandlerContext;
import com.shouqianba.trade.fund.settlement.common.util.JsonUtils;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventContentFundChannelNotifyVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.SettlementBatchAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.query.SettlementBatchAggrQuery;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/3/17 Time: 16:20 PM
 */
@Getter
public class FundChannelNotifyEventContext extends EventStrategyContext<EventContentFundChannelNotifyVO> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.SHARING_SETTLE;

    private EventContentFundChannelNotifyVO eventContent;
    private SettlementBatchAggrRoot settlementBatchAggrRoot;

    static {
        registerContext(EVENT_TYPE, new FundChannelNotifyEventContext());
    }

    private FundChannelNotifyEventContext() {
    }

    private FundChannelNotifyEventContext(EventAggrRoot eventAggrRoot) {
        super(eventAggrRoot);
    }

    @Override
    public EventStrategyContext<EventContentFundChannelNotifyVO> rebuildContext(EventHandlerContext context) {
        return new FundChannelNotifyEventContext(context.getEventAggrRoot());
    }

    @Override
    public EventContentFundChannelNotifyVO getBizContent() {
        if (Objects.isNull(eventContent)) {
            eventContent = JsonUtils.parseObject(getEventAggrRoot()
                            .getContent()
                            .getBizContent()
                    , EventContentFundChannelNotifyVO.class);
        }
        return eventContent;
    }

    public void bindSettlementBatchAggrRoot(SettlementBatchAggrRoot settlementBatchAggrRoot) {
        this.settlementBatchAggrRoot = settlementBatchAggrRoot;
    }

    public SettlementBatchAggrQuery genSettlementBatchAggrQuery() {
        return SettlementBatchAggrQuery
                .builder()
                .id(getEventAggrRoot().getAssociatedSnNum())
                .build();
    }

}
