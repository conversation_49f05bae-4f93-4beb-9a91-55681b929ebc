package com.shouqianba.trade.fund.settlement.application.adapter.rpc;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.trade.fund.settlement.api.WithdrawService;
import com.shouqianba.trade.fund.settlement.api.request.WithdrawBalanceQueryRequest;
import com.shouqianba.trade.fund.settlement.api.request.WithdrawCreateRequest;
import com.shouqianba.trade.fund.settlement.api.result.WithdrawBalanceQueryResult;
import com.shouqianba.trade.fund.settlement.api.result.WithdrawCreateResult;
import com.shouqianba.trade.fund.settlement.application.biz.context.impl.withdraw.WithdrawBalanceQueryContext;
import com.shouqianba.trade.fund.settlement.application.biz.context.impl.withdraw.WithdrawCreateContext;
import com.shouqianba.trade.fund.settlement.application.biz.impl.withdraw.WithdrawBalanceQueryBiz;
import com.shouqianba.trade.fund.settlement.application.biz.impl.withdraw.WithdrawCreateBiz;
import com.shouqianba.trade.fund.settlement.common.util.ResultUtils;
import com.wosai.general.result.SingleResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 提现服务实现
 *
 * <AUTHOR> Date: 2025/6/15 Time: 10:20 AM
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class WithdrawServiceImpl implements WithdrawService {

    @Resource
    private WithdrawBalanceQueryBiz withdrawBalanceQueryBiz;

    @Resource
    private WithdrawCreateBiz withdrawCreateBiz;

    @Override
    public SingleResult<WithdrawCreateResult> createWithdraw(WithdrawCreateRequest request) {
        WithdrawCreateContext context = WithdrawCreateContext.newInstance(request);
        withdrawCreateBiz.process(context);
        return ResultUtils.buildSuccessfulSingleResult(context.genResult());
    }

    @Override
    public SingleResult<WithdrawBalanceQueryResult> queryWithdrawBalance(WithdrawBalanceQueryRequest request) {
        WithdrawBalanceQueryContext context = WithdrawBalanceQueryContext.newInstance(request);
        withdrawBalanceQueryBiz.process(context);
        return ResultUtils.buildSuccessfulSingleResult(context.genResult());
    }
}