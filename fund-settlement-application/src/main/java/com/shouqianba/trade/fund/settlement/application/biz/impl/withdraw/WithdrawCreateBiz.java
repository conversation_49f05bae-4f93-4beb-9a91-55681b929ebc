package com.shouqianba.trade.fund.settlement.application.biz.impl.withdraw;

import com.shouqianba.trade.fund.settlement.application.biz.BaseBiz;
import com.shouqianba.trade.fund.settlement.application.biz.context.impl.withdraw.WithdrawCreateContext;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.BrandSettleClient;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res.SettlementWithdrawSubmitResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 提现创建业务实现
 *
 * <AUTHOR> Date: 2025/6/15 Time: 10:20 AM
 */
@Slf4j
@Component
public class WithdrawCreateBiz extends BaseBiz<WithdrawCreateContext> {

    @Resource
    private BrandSettleClient brandSettleClient;

    @Override
    protected void doBiz(WithdrawCreateContext context) {
        log.info("开始处理提现申请, request: {}", context.getRequest());
        
        // 调用品牌结算客户端提交提现申请
        SettlementWithdrawSubmitResult withdrawSubmitResult = 
                brandSettleClient.submitWithdraw(context.genWithdrawSubmitRequest());
        
        // 绑定提现结果到上下文
        context.bindWithdrawSubmitResult(withdrawSubmitResult);
        
        log.info("提现申请处理完成, result: {}", withdrawSubmitResult);
    }
}