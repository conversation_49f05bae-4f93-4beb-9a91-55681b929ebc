package com.shouqianba.trade.fund.settlement.application.biz.impl.handler.context.impl;

import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.context.EventStrategyContext;
import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.holder.EventHandlerContext;
import com.shouqianba.trade.fund.settlement.common.util.JsonUtils;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventContentClearingNotifyVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.SettlementBatchAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.query.SettlementBatchAggrQuery;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/3/17 Time: 16:20 PM
 */
@Getter
public class ClearingNotifyEventContext extends EventStrategyContext<EventContentClearingNotifyVO> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.SHARING_SETTLE;

    private EventContentClearingNotifyVO eventContent;
    private SettlementBatchAggrRoot settlementBatchAggrRoot;

    static {
        registerContext(EVENT_TYPE, new ClearingNotifyEventContext());
    }

    private ClearingNotifyEventContext() {
    }

    private ClearingNotifyEventContext(EventAggrRoot eventAggrRoot) {
        super(eventAggrRoot);
    }

    @Override
    public EventStrategyContext<EventContentClearingNotifyVO> rebuildContext(EventHandlerContext context) {
        return new ClearingNotifyEventContext(context.getEventAggrRoot());
    }

    @Override
    public EventContentClearingNotifyVO getBizContent() {
        if (Objects.isNull(eventContent)) {
            eventContent = JsonUtils.parseObject(getEventAggrRoot()
                            .getContent()
                            .getBizContent()
                    , EventContentClearingNotifyVO.class);
        }
        return eventContent;
    }

    public void bindSettlementBatchAggrRoot(SettlementBatchAggrRoot settlementBatchAggrRoot) {
        this.settlementBatchAggrRoot = settlementBatchAggrRoot;
    }

    public boolean isClearingSuccess() {
        return getBizContent().isClearingSuccess();
    }

    public SettlementBatchAggrQuery genSettlementBatchAggrQuery() {
        return SettlementBatchAggrQuery.builder()
//                .fundBathcIdgetBizContent().getBatchId())
               .build();
    }
}
