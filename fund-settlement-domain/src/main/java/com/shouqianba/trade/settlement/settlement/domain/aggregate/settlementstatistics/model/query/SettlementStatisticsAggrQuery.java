package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.query;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;

/***
 * <AUTHOR> Date: 2025/07/15 Time: 10:34 AM
 */
@Data
@Builder
public class SettlementStatisticsAggrQuery {

    /**
     * 结算统计ID
     */
    private Long id;

    /**
     * 统计类型
     */
    private Byte type;

    /**
     * 收款方主体类型
     */
    private Byte payeeType;

    /**
     * 品牌编号
     */
    private String brandSn;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 门店编号
     */
    private String storeSn;

    /**
     * 收单机构
     */
    private Integer acquiringCompany;

    /**
     * 统计日期
     */
    private LocalDate statisticsDate;

    /**
     * 统计日期-开始
     */
    private LocalDate statisticsDateStart;

    /**
     * 统计日期-结束
     */
    private LocalDate statisticsDateEnd;

    /**
     * 创建时间-开始
     */
    private LocalDate ctimeStart;

    /**
     * 创建时间-结束
     */
    private LocalDate ctimeEnd;

    /**
     * 分页-页码
     */
    private Integer pageNum;

    /**
     * 分页-每页大小
     */
    private Integer pageSize;
}