package com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/3/10 Time: 10:20 AM
 */
@Getter
public enum BatchClearingSettlementStatusEnum {

    PENDING((byte) 2, "待处理"),          // 原 SQB
    PROCESSING((byte) 3, "处理中"),       // 原 MEI_TUAN
    PROCESSED((byte) 4, "已处理"),        // 原 ELM
    FAILED((byte) 7, "处理失败");          // 原 BRAND_UPLOAD

    private final byte code;
    private final String desc;

    BatchClearingSettlementStatusEnum(byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public byte getCode() {
        return code;
    }

    @JsonCreator
    public static BatchClearingSettlementStatusEnum ofCode(Byte code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (BatchClearingSettlementStatusEnum type : values()) {
            if (Objects.equals(type.getCode(), code)) {
                return type;
            }
        }
        return null;
    }

    public Integer getCodeIntValue() {
        return (int) code;
    }
}
