package com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model;


import com.shouqianba.trade.fund.settlement.common.exception.FundSettlementBizException;
import com.shouqianba.trade.fund.settlement.common.exception.enums.FundSettlementRespCodeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseEntity;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.EventStateEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.DelayRuleVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventContentVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo.EventExtVO;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/6/16 Time: 6:53 PM
 */
@Getter
@Setter(AccessLevel.PACKAGE)
@EqualsAndHashCode(callSuper = true)
public class EventAggrRoot extends BaseEntity {
    private static final String DEFAULT_MQ_KEY = "2718281828459";
    private static final String CHANGED_TO_PENDING_MANUAL_PROCESSING_RESULT = "变更为手动待处理";

    @NotNull(message = "事件类型不能为空")
    private EventTypeEnum type;
    @NotNull(message = "事件状态不能为空")
    private EventStateEnum state;
    @NotNull(message = "事件结果不能为空")
    private String result;
    @NotNull(message = "下次处理时间不能为空")
    private LocalDateTime nextProcessTime;
    @NotNull(message = "事件关联单号不能为空")
    private String associatedSn;

    @Valid
    @NotNull(message = "事件内容不能为空")
    private EventContentVO content;
    @Valid
    @NotNull(message = "延迟规则不能为空")
    private DelayRuleVO delayRule;
    @Valid
    @NotNull(message = "事件扩展字段不能为空")
    private EventExtVO ext;

    protected EventAggrRoot() {
    }


    public static EventAggrRoot newEmptyInstance() {
        return new EventAggrRoot();
    }


    public void checkExist() {
        if (isNotExist()) {
            throw new FundSettlementBizException(FundSettlementRespCodeEnum.EVENT_NOT_EXIST);
        }
    }

    public boolean isProcessed() {
        return isExist() && Objects.equals(state, EventStateEnum.PROCESSED);
    }


    public Long getAssociatedSnNum() {
        return Long.parseLong(associatedSn);
    }

    public void processSuccess(String result) {
        this.result = result;
        state = EventStateEnum.PROCESSED;
        delayRule = delayRule.increaseProcessedCount();
        modifyUpdated();
    }

    public void processFailure(String result) {
        this.result = result;
        nextProcessTime = nextProcessTime.plusSeconds(delayRule.getNextProcessIntervalSecond());
        delayRule = delayRule.increaseProcessedCount();
        if (delayRule.isReachProcessedMax()) {
            state = EventStateEnum.EXCEEDED_PROCESSING_LIMIT;
        }
        modifyUpdated();
    }

    public void processStageCompleted(String result) {
        this.state = EventStateEnum.STAGE_COMPLETED;
        this.result = result;
        delayRule = delayRule.increaseProcessedCount();
        modifyUpdated();
    }

    public void updateToPendingManualProcessing() {
        this.state = EventStateEnum.PENDING_MANUAL_PROCESSING;
        this.result = CHANGED_TO_PENDING_MANUAL_PROCESSING_RESULT;
        modifyUpdated();
    }

    public boolean isNeedAlert() {
        return !Objects.equals(state, EventStateEnum.PROCESSED)
                && delayRule.getProcessedCount() >= delayRule.getAlarmThreshold();
    }

    public String getMqKey() {
        if (Objects.isNull(ext)) {
            return null;
        }
        String mqKey = ext.getMqKey();
        if (StringUtils.isEmpty(mqKey)) {
            return null;
        }
        return mqKey;
    }

    public String getMqKeyNullDefault() {
        String mqKey = getMqKey();
        if (Objects.isNull(mqKey)) {
            return DEFAULT_MQ_KEY;
        }
        return mqKey;
    }

    public boolean isNeedIndependentProcess() {
        return EventTypeEnum.isNeedIndependentProcess(type);
    }

    public void updateContent(EventContentVO content) {
        if (Objects.isNull(content)) {
            return;
        }
        this.content = content;
    }


    public boolean isMchHotAccountEvent() {
        return EventTypeEnum.isMchHotAccountEvent(type);
    }

    @Override
    protected void setId(Long id) {
        super.id = id;
    }

    @Override
    protected void setCreated(LocalDateTime created) {
        super.created = created;
    }

    @Override
    protected void setUpdated(LocalDateTime updated) {
        super.updated = updated;
    }

    @Override
    protected void setVersion(Long version) {
        super.version = version;
    }

    public void updateStatus(EventStateEnum eventStateEnum) {
        if (Objects.isNull(eventStateEnum)) {
            return;
        }
        if (Objects.equals(eventStateEnum, this.state)) {
            return;
        }
        this.state = eventStateEnum;
        modifyUpdated();
    }

    public void updateResult(String result) {
        this.result = result;
    }
}
