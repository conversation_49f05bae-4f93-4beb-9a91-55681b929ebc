package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.BatchClearingSettlementStatusEnum;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;

/***
 * <AUTHOR> Date: 2025/5/19 Time: 4:34 PM
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
public class SettlementBatchBizInfoVO extends BaseVO<SettlementBatchBizInfoVO> {

    private BatchClearingSettlementStatusEnum clearingStatus;

    private String settleDate;

    @NotNull(message = "收单机构不能为空")
    private Integer acquiringCompany;

    public static SettlementBatchBizInfoVO newEmptyInstance() {
        return SettlementBatchBizInfoVO
                .builder()
                .build();
    }

    @Override
    protected SettlementBatchBizInfoVO doReplaceNotNull(SettlementBatchBizInfoVO vo) {
        SettlementBatchBizInfoVOBuilder builder = toBuilder();
        if (vo.getClearingStatus() != null) {
            builder.clearingStatus(vo.getClearingStatus());
        }

        return builder.build();
    }

    public Object isNeedEntrySettle() {
        return clearingStatus == BatchClearingSettlementStatusEnum.PENDING || clearingStatus == BatchClearingSettlementStatusEnum.FAILED;
    }
}
