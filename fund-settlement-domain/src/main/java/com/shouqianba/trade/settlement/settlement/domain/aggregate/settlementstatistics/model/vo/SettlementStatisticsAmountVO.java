package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo;

import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;

/**
 * 结算统计金额VO
 *
 * <AUTHOR> Date: 2025/07/15 Time: 10:34 AM
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
public class SettlementStatisticsAmountVO extends BaseVO<SettlementStatisticsAmountVO> {
    /**
     * 总金额
     */
    @NotNull(message = "总金额不能为空")
    private Long totalAmount;


    @Override
    protected SettlementStatisticsAmountVO doReplaceNotNull(SettlementStatisticsAmountVO vo) {
        return null;
    }

    public void addAmount(Long amount) {
        this.totalAmount += amount;
    }
}