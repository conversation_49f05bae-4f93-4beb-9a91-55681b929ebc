package com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotEmpty;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventContentClearingNotifyVO extends BaseVO<EventContentClearingNotifyVO> {

    @NotEmpty(message = "brand_id不能为空")
    private String brandId;

    @NotEmpty(message = "date不能为空")
    private String date;

    @NotEmpty(message = "action_id不能为空")
    private String actionId;

    @NotEmpty(message = "client_sn不能为空")
    private String clientSn;

    @NotEmpty(message = "status不能为空")
    private Integer status;

    @NotEmpty(message = "start_time不能为空")
    private Long finishTime;

    @NotEmpty(message = "end_time不能为空")
    private String failMsg;

    @NotEmpty(message = "clearing_result_file不能为空")
    private String clearingResultFile;

    @Override
    protected EventContentClearingNotifyVO doReplaceNotNull(EventContentClearingNotifyVO vo) {
        EventContentClearingNotifyVO.EventContentClearingNotifyVOBuilder builder = toBuilder();

        if (Objects.nonNull(vo.getBrandId())) {
            builder.brandId(vo.getBrandId());
        }

        if (Objects.nonNull(vo.getDate())) {
            builder.date(vo.getDate());
        }

        if (Objects.nonNull(vo.getActionId())) {
            builder.actionId(vo.getActionId());
        }

        if (Objects.nonNull(vo.getClientSn())) {
            builder.clientSn(vo.getClientSn());
        }

        if (Objects.nonNull(vo.getClearingResultFile())) {
            builder.clearingResultFile(vo.getClearingResultFile());
        }

        if (Objects.nonNull(vo.getFailMsg())) {
            builder.failMsg(vo.getFailMsg());
        }

        if (Objects.nonNull(vo.getFinishTime())) {
            builder.finishTime(vo.getFinishTime());
        }

        if (Objects.nonNull(vo.getStatus())) {
            builder.status(vo.getStatus());
        }

        if (Objects.nonNull(vo.getActionId())) {
            builder.actionId(vo.getActionId());
        }

        if (Objects.nonNull(vo.getDate())) {
            builder.date(vo.getDate());
        }

        if (Objects.nonNull(vo.getBrandId())) {
            builder.brandId(vo.getBrandId());
        }

        if (Objects.nonNull(vo.getClientSn())) {
            builder.clientSn(vo.getClientSn());
        }
        return builder.build();

    }

    public Long getClientSnNum() {
        if (clientSn == null) {
            return null;
        }
        return Long.parseLong(clientSn);
    }

    public boolean isClearingSuccess() {
        return status == 1;
    }

}