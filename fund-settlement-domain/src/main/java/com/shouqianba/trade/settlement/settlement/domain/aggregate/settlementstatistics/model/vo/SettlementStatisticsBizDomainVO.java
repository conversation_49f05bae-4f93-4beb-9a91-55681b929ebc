package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo;

import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

/**
 * 结算统计业务领域信息VO
 *
 * <AUTHOR> Date: 2025/07/15 Time: 10:34 AM
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
public class SettlementStatisticsBizDomainVO extends BaseVO<SettlementStatisticsBizDomainVO> {
    /**
     * 业务领域
     */
    private String bizDomain;

    /**
     * 业务子领域
     */
    private String bizSubDomain;

    public static SettlementStatisticsBizDomainVO newEmptyInstance() {
        return SettlementStatisticsBizDomainVO.builder().build();
    }

    @Override
    protected SettlementStatisticsBizDomainVO doReplaceNotNull(SettlementStatisticsBizDomainVO vo) {
        return null;
    }
}