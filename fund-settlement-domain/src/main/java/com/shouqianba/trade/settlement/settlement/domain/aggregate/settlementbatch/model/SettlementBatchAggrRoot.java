package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model;

import com.shouqianba.trade.fund.settlement.common.exception.FundSettlementBizException;
import com.shouqianba.trade.fund.settlement.common.exception.enums.FundSettlementRespCodeEnum;
import com.shouqianba.trade.fund.settlement.common.support.PropertiesSupporter;
import com.shouqianba.trade.fund.settlement.common.util.ApplicationContextUtils;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseEntity;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.BatchClearingSettlementStatusEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.enums.BatchTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.enums.SettlementBatchStatusEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.vo.SettlementBatchAccountInfoVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.vo.SettlementBatchAmountVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.vo.SettlementBatchBizInfoVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.AccountTypeEnum;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Objects;

/***
 * <AUTHOR> Date: 2025/5/19 Time: 4:34 PM
 */
@Getter
@Setter(AccessLevel.PACKAGE)
@EqualsAndHashCode(callSuper = true)
public class SettlementBatchAggrRoot extends BaseEntity {
    private static final String FILE_PATH_TEMPLATE = "fund/%s/export/clearing/%s/%s";

    /**
     * 批次类型: 1-入账结算批次
     */
    @NotNull(message = "批次类型不能为空")
    private BatchTypeEnum type;

    /**
     * 付款方信息
     */
    @NotNull(message = "付款方类型不能为空")
    private AccountTypeEnum fromType;

    /**
     * 付款方品牌编号
     */
    @NotEmpty(message = "付款方品牌编号不能为空")
    private String fromBrandSn;


    /**
     * 付款方商户编号
     */
    @NotEmpty(message = "付款方商户编号不能为空")
    private String fromMerchantSn;

    /**
     * 付款方门店编号
     */
    private String fromStoreSn;

    /**
     * 付款方信息
     */
    @Valid
    @NotNull(message = "付款方信息不能为空")
    private SettlementBatchAccountInfoVO fromInfo;


    /**
     * 收款方信息
     */
    @NotNull(message = "收款方类型不能为空")
    private AccountTypeEnum toType;


    /**
     * 收款方品牌编号
     */
    @NotEmpty(message = "收款方品牌编号不能为空")
    private String toBrandSn;


    /**
     * 收款方商户编号
     */
    @NotEmpty(message = "收款方商户编号不能为空")
    private String toMerchantSn;


    /**
     * 收款方门店编号
     */
    private String toStoreSn;

    @Valid
    @NotNull(message = "收款方信息不能为空")
    private SettlementBatchAccountInfoVO toInfo;

    /**
     * 金额信息
     */
    @Valid
    @NotNull(message = "金额信息不能为空")
    private SettlementBatchAmountVO amount;

    /**
     * 业务信息
     */
    @Valid
    private SettlementBatchBizInfoVO bizInfo;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    private SettlementBatchStatusEnum status;

    protected SettlementBatchAggrRoot() {
    }

    public static SettlementBatchAggrRoot newEmptyInstance() {
        return new SettlementBatchAggrRoot();
    }

    public void checkExist() {
        if (isNotExist()) {
            throw new FundSettlementBizException(FundSettlementRespCodeEnum.SETTLEMENT_BATCH_NOT_EXIST);
        }
    }

    public void updateStatus(SettlementBatchStatusEnum status) {
        if (Objects.isNull(status)) {
            return;
        }
        this.status = status;
        modifyUpdated();
    }

    public void updateFromInfo(SettlementBatchAccountInfoVO fromInfo) {
        if (Objects.isNull(fromInfo)) {
            return;
        }
        this.fromInfo = fromInfo;
        modifyUpdated();
    }

    public void updateToInfo(SettlementBatchAccountInfoVO toInfo) {
        if (Objects.isNull(toInfo)) {
            return;
        }
        this.toInfo = toInfo;
        modifyUpdated();
    }

    public void updateAmount(SettlementBatchAmountVO amount) {
        if (Objects.isNull(amount)) {
            return;
        }
        this.amount = amount;
        modifyUpdated();
    }

    public void updateBizInfo(SettlementBatchBizInfoVO bizInfo) {
        if (Objects.isNull(bizInfo)) {
            return;
        }
        this.bizInfo = bizInfo;
        modifyUpdated();
    }

    public boolean isPending() {
        return Objects.equals(this.status, (byte) 1);
    }

    public boolean isProcessing() {
        return Objects.equals(this.status, (byte) 2);
    }

    public boolean isSuccess() {
        return Objects.equals(this.status, (byte) 3);
    }

    public boolean isFailed() {
        return Objects.equals(this.status, (byte) 4);
    }

    @Override
    protected void setId(Long id) {
        super.id = id;
    }

    @Override
    protected void setCreated(LocalDateTime created) {
        super.created = created;
    }

    @Override
    protected void setUpdated(LocalDateTime updated) {
        super.updated = updated;
    }

    @Override
    protected void setVersion(Long version) {
        super.version = version;
    }

    public boolean isCreateFinished() {
        return !Objects.equals(status, SettlementBatchStatusEnum.ENTRY);
    }

    public void checkCreateFinished() {
        if (isCreateFinished()) {
            throw new FundSettlementBizException(FundSettlementRespCodeEnum.SETTLEMENT_BATCH_FINISHED_CREATE);
        }
    }

    public boolean isNeedEntrySettle() {
        return Objects.nonNull(bizInfo) && Objects.nonNull(bizInfo.isNeedEntrySettle());
    }

    public void updateEntrySettleStatus(BatchClearingSettlementStatusEnum batchClearingSettlementStatusEnum) {
        if (Objects.isNull(batchClearingSettlementStatusEnum)) {
            return;
        }
        this.bizInfo.replaceNotNull(SettlementBatchBizInfoVO.builder().clearingStatus(batchClearingSettlementStatusEnum).build());
        modifyUpdated();
    }

    public String genFilePath(String fileName) {
        String env = ApplicationContextUtils
                .getBean(PropertiesSupporter.class).getEnv();
        return String.format(FILE_PATH_TEMPLATE, env, fromBrandSn, fileName);
    }
}
