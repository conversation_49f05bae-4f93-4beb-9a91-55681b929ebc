package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.query;

import lombok.Builder;
import lombok.Data;

/***
 * <AUTHOR> Date: 2025/04/24 Time: 04:34 PM
 */
@Data
@Builder
public class SettlementFlowAggrQuery {

    /**
     * 结算流水ID
     */
    private Long id;

    /**
     * 流水类型
     */
    private Byte type;

    /**
     * 业务交易号
     */
    private String transSn;

    /**
     * 业务订单号
     */
    private String orderSn;

    /**
     * 业务订单号
     */
    private String flowOrderNo;

    /**
     * 批次号
     */
    private String batchSn;

    /**
     * 批次ID
     */
    private Long batchId;

    /**
     * 资金池ID
     */
    private Long poolId;

    /**
     * 付款方类型
     */
    private Byte fromType;

    /**
     * 付款方编号
     */
    private String fromSn;

    /**
     * 付款方品牌编号
     */
    private String fromBrandSn;

    /**
     * 付款方商户编号
     */
    private String fromMerchantSn;

    /**
     * 付款方门店编号
     */
    private String fromStoreSn;

    /**
     * 收款方类型
     */
    private Byte toType;

    /**
     * 收款方编号
     */
    private String toSn;

    /**
     * 收款方品牌编号
     */
    private String toBrandSn;

    /**
     * 收款方商户编号
     */
    private String toMerchantSn;

    /**
     * 收款方门店编号
     */
    private String toStoreSn;

    /**
     * 状态
     */
    private Byte status;

    // 分页相关字段
    private String cursorField;
    private String endCursor;
    private Integer querySize;

    // 排序相关字段
    private String sortField;
    private boolean isDesc;
}
