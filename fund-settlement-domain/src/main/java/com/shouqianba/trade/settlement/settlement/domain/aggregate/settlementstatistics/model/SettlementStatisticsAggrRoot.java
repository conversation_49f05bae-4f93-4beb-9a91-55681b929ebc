package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model;

import com.shouqianba.trade.fund.settlement.common.exception.FundSettlementBizException;
import com.shouqianba.trade.fund.settlement.common.exception.enums.FundSettlementRespCodeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseEntity;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.AccountTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.enums.StatisticsTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo.SettlementStatisticsAmountVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo.SettlementStatisticsBizDomainVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo.SettlementStatisticsExtVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo.SettlementStatisticsPayeeInfoVO;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

/***
 * <AUTHOR> Date: 2025/07/15 Time: 10:34 AM
 */
@Getter
@Setter(AccessLevel.PACKAGE)
@EqualsAndHashCode(callSuper = true)
public class SettlementStatisticsAggrRoot extends BaseEntity {

    /**
     * 统计类型：1：商户维度清分统计，2：商户维度分账统计
     */
    @NotNull(message = "统计类型不能为空")
    private StatisticsTypeEnum type;

    /**
     * 收款方主体类型：1：品牌，2：商户，3：门店
     */
    @NotNull(message = "收款方主体类型不能为空")
    private AccountTypeEnum payeeType;

    /**
     * 品牌编号
     */
    @NotNull(message = "品牌编号不能为空")
    private String brandSn;

    /**
     * 商户编号
     */
    private String merchantSn;

    /**
     * 门店编号
     */
    private String storeSn;

    /**
     * 收款方信息
     */
    @Valid
    @NotNull(message = "收款方信息不能为空")
    private SettlementStatisticsPayeeInfoVO payeeInfo;

    /**
     * 收单机构
     */
    @NotNull(message = "收单机构不能为空")
    private Short acquiringCompany;

    /**
     * 统计日期
     */
    @NotNull(message = "统计日期不能为空")
    private LocalDate statisticsDate;

    /**
     * 金额信息
     */
    @Valid
    @NotNull(message = "金额信息不能为空")
    private SettlementStatisticsAmountVO amount;

    /**
     * 领域信息
     */
    @Valid
    private SettlementStatisticsBizDomainVO bizDomain;

    /**
     * 扩展字段
     */
    @Valid
    private SettlementStatisticsExtVO ext;

    protected SettlementStatisticsAggrRoot() {
    }

    public static SettlementStatisticsAggrRoot newEmptyInstance() {
        return new SettlementStatisticsAggrRoot();
    }

    public void checkExist() {
        if (isNotExist()) {
            throw new FundSettlementBizException(FundSettlementRespCodeEnum.SETTLEMENT_STATISTICS_NOT_EXIST);
        }
    }

    @Override
    protected void setId(Long id) {
        super.id = id;
    }

    @Override
    protected void setCreated(LocalDateTime created) {
        super.created = created;
    }

    @Override
    protected void setUpdated(LocalDateTime updated) {
        super.updated = updated;
    }

    @Override
    protected void setVersion(Long version) {
        super.version = version;
    }

    public void addAmount(Long amount) {
        this.amount.addAmount(amount);
    }
}