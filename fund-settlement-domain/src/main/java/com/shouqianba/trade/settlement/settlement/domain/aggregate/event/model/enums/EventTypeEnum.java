package com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums;

import lombok.Getter;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> Date: 2023/6/16 Time: 6:57 PM
 */
@Getter
public enum EventTypeEnum {
    SHARING_SETTLE((byte) 1, "分账结算"),
    BATCH_ENTRY_SETTLE((byte) 2, "批次入账结算"),
    FUND_CHANNEL_NOTIFY((byte) 10, "资金动账通知"),
    CLEARING_NOTIFY((byte) 11, "清分通知"),
    ;

    //涉及外部调用的事件，需事件处理器独立处理事务性，不走统一处理
    private static final Set<EventTypeEnum> IS_INDEPENDENT_PROCESS_EVENTS = Set.of();

    private static final Set<EventTypeEnum> MCH_HOT_ACCOUNT_EVENTS =
            Set.of(BATCH_ENTRY_SETTLE, SHARING_SETTLE);


    private final byte code;
    private final String desc;


    EventTypeEnum(byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EventTypeEnum ofCode(Byte code) {
        if (Objects.isNull(code)) {
            return null;
        }
        EventTypeEnum[] typeEnums = EventTypeEnum.values();
        for (EventTypeEnum type : typeEnums) {
            if (Objects.equals(type.getCode(), code)) {
                return type;
            }
        }

        return null;
    }

    public static boolean isNeedIndependentProcess(EventTypeEnum type) {
        return IS_INDEPENDENT_PROCESS_EVENTS.contains(type);
    }

    public static boolean isMchHotAccountEvent(EventTypeEnum type) {
        return MCH_HOT_ACCOUNT_EVENTS.contains(type);
    }

}
