package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo;

import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

/**
 * 结算统计收款方信息VO
 *
 * <AUTHOR> Date: 2025/07/15 Time: 10:34 AM
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
public class SettlementStatisticsPayeeInfoVO extends BaseVO<SettlementStatisticsPayeeInfoVO> {
    /**
     * 收款方名称
     */
    private String payeeName;

    /**
     * 收款方账户
     */
    private String payeeAccount;

    @Override
    protected SettlementStatisticsPayeeInfoVO doReplaceNotNull(SettlementStatisticsPayeeInfoVO vo) {
        return null;
    }
}