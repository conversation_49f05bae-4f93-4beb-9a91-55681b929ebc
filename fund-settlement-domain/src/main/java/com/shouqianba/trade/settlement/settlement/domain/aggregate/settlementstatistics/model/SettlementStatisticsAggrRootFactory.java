package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model;

import com.shouqianba.trade.fund.settlement.common.exception.FundSettlementBizException;
import com.shouqianba.trade.fund.settlement.common.exception.enums.FundSettlementRespCodeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseFactory;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.AccountTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.enums.StatisticsTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo.SettlementStatisticsAmountVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo.SettlementStatisticsBizDomainVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo.SettlementStatisticsExtVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo.SettlementStatisticsPayeeInfoVO;
import com.wosai.general.util.validation.ValidationUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/***
 * <AUTHOR> Date: 2025/07/15 Time: 10:34 AM
 */
public class SettlementStatisticsAggrRootFactory extends BaseFactory {
    public static SettlementStatisticsAggrRootBuilder builder() {
        return new SettlementStatisticsAggrRootBuilder(SettlementStatisticsAggrRoot.newEmptyInstance());
    }

    public static class SettlementStatisticsAggrRootBuilder extends BaseBuilder<SettlementStatisticsAggrRoot, SettlementStatisticsAggrRootCoreBuilder, SettlementStatisticsAggrRootOptionalBuilder> {
        private SettlementStatisticsAggrRootCoreBuilder settlementStatisticsAggrRootCoreBuilder;
        private SettlementStatisticsAggrRootOptionalBuilder settlementStatisticsAggrRootOptionalBuilder;

        protected SettlementStatisticsAggrRootBuilder(SettlementStatisticsAggrRoot settlementStatisticsAggrRoot) {
            super(settlementStatisticsAggrRoot);
        }

        @Override
        public SettlementStatisticsAggrRootCoreBuilder coreBuilder() {
            if (Objects.isNull(settlementStatisticsAggrRootCoreBuilder)) {
                settlementStatisticsAggrRootCoreBuilder = new SettlementStatisticsAggrRootCoreBuilder(aggrRoot);
            }
            return settlementStatisticsAggrRootCoreBuilder;
        }

        @Override
        public SettlementStatisticsAggrRootOptionalBuilder optionalBuilder() {
            if (Objects.isNull(settlementStatisticsAggrRootOptionalBuilder)) {
                settlementStatisticsAggrRootOptionalBuilder = new SettlementStatisticsAggrRootOptionalBuilder(aggrRoot);
            }
            return settlementStatisticsAggrRootOptionalBuilder;
        }

        @Override
        protected void checkParams() {
            ValidationUtils.ValidationResult result = ValidationUtils.validate(aggrRoot);
            if (result.isInvalid()) {
                throw new FundSettlementBizException(FundSettlementRespCodeEnum.ILLEGAL_ARGUMENT
                        , result.getMsg());
            }
        }
    }

    public static class SettlementStatisticsAggrRootCoreBuilder extends SettlementStatisticsAggrRootBuilder implements BaseCoreBuilder {

        protected SettlementStatisticsAggrRootCoreBuilder(SettlementStatisticsAggrRoot settlementStatisticsAggrRoot) {
            super(settlementStatisticsAggrRoot);
        }

        public SettlementStatisticsAggrRootCoreBuilder id(Long id) {
            aggrRoot.setId(id);
            return this;
        }

        public SettlementStatisticsAggrRootCoreBuilder type(StatisticsTypeEnum type) {
            aggrRoot.setType(type);
            return this;
        }

        public SettlementStatisticsAggrRootCoreBuilder payeeType(AccountTypeEnum payeeType) {
            aggrRoot.setPayeeType(payeeType);
            return this;
        }

        public SettlementStatisticsAggrRootCoreBuilder brandSn(String brandSn) {
            aggrRoot.setBrandSn(brandSn);
            return this;
        }

        public SettlementStatisticsAggrRootCoreBuilder merchantSn(String merchantSn) {
            aggrRoot.setMerchantSn(merchantSn);
            return this;
        }

        public SettlementStatisticsAggrRootCoreBuilder storeSn(String storeSn) {
            aggrRoot.setStoreSn(storeSn);
            return this;
        }

        public SettlementStatisticsAggrRootCoreBuilder payeeInfo(SettlementStatisticsPayeeInfoVO payeeInfo) {
            aggrRoot.setPayeeInfo(payeeInfo);
            return this;
        }

        public SettlementStatisticsAggrRootCoreBuilder acquiringCompany(Short acquiringCompany) {
            aggrRoot.setAcquiringCompany(acquiringCompany);
            return this;
        }

        public SettlementStatisticsAggrRootCoreBuilder statisticsDate(LocalDate statisticsDate) {
            aggrRoot.setStatisticsDate(statisticsDate);
            return this;
        }

        public SettlementStatisticsAggrRootCoreBuilder amount(SettlementStatisticsAmountVO amount) {
            aggrRoot.setAmount(amount);
            return this;
        }
    }

    public static class SettlementStatisticsAggrRootOptionalBuilder extends SettlementStatisticsAggrRootBuilder implements BaseOptionalBuilder {

        protected SettlementStatisticsAggrRootOptionalBuilder(SettlementStatisticsAggrRoot settlementStatisticsAggrRoot) {
            super(settlementStatisticsAggrRoot);
        }

        public SettlementStatisticsAggrRootOptionalBuilder bizDomain(SettlementStatisticsBizDomainVO bizDomain) {
            aggrRoot.setBizDomain(bizDomain);
            return this;
        }

        public SettlementStatisticsAggrRootOptionalBuilder ext(SettlementStatisticsExtVO ext) {
            aggrRoot.setExt(ext);
            return this;
        }

        public SettlementStatisticsAggrRootOptionalBuilder created(LocalDateTime created) {
            aggrRoot.setCreated(created);
            return this;
        }

        public SettlementStatisticsAggrRootOptionalBuilder updated(LocalDateTime updated) {
            aggrRoot.setUpdated(updated);
            return this;
        }

        public SettlementStatisticsAggrRootOptionalBuilder version(Long version) {
            aggrRoot.setVersion(version);
            return this;
        }

        @Override
        public void initOptional() {
            LocalDateTime created = aggrRoot.getCreated();
            LocalDateTime updated = aggrRoot.getUpdated();
            Long version = aggrRoot.getVersion();

            LocalDateTime currentDateTime = LocalDateTime.now();
            if (Objects.isNull(created)) {
                aggrRoot.setCreated(currentDateTime);
            }
            if (Objects.isNull(updated)) {
                aggrRoot.setUpdated(currentDateTime);
            }
            if (Objects.isNull(version)) {
                aggrRoot.setVersion(0L);
            }
            if (Objects.isNull(aggrRoot.getBizDomain())) {
                aggrRoot.setBizDomain(SettlementStatisticsBizDomainVO.newEmptyInstance());
            }
            if (Objects.isNull(aggrRoot.getExt())) {
                aggrRoot.setExt(SettlementStatisticsExtVO.newEmptyInstance());
            }
        }
    }
}