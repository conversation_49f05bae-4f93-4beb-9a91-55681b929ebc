package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model;

import com.shouqianba.trade.fund.settlement.common.exception.FundSettlementBizException;
import com.shouqianba.trade.fund.settlement.common.exception.enums.FundSettlementRespCodeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseEntity;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.AccountTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.FlowTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.SettlementStatusEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowAccountInfoVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowAmountVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowBizInfoVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowTradeInfoVO;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/***
 * <AUTHOR> Date: 2025/04/24 Time: 04:34 PM
 */
@Getter
@Setter(AccessLevel.PACKAGE)
@EqualsAndHashCode(callSuper = true)
public class SettlementFlowAggrRoot extends BaseEntity {

    // 基础业务标识
    @NotNull(message = "流水类型不能为空")
    private FlowTypeEnum type;

    @NotEmpty(message = "业务交易号不能为空")
    private String transSn;

    @NotEmpty(message = "业务订单号不能为空")
    private String orderSn;

    @NotNull(message = "批次ID不能为空")
    private Long batchId;

    @NotNull(message = "资金池ID不能为空")
    private Long poolId;

    // 付款方信息
    @NotNull(message = "付款方类型不能为空")
    private AccountTypeEnum fromType;

    @NotEmpty(message = "付款方品牌编号不能为空")
    private String fromBrandSn;

    @NotEmpty(message = "付款方商户编号不能为空")
    private String fromMerchantSn;

    private String fromStoreSn;

    @Valid
    @NotNull(message = "付款方信息不能为空")
    private SettlementFlowAccountInfoVO fromInfo;

    // 收款方信息
    @NotNull(message = "收款方类型不能为空")
    private AccountTypeEnum toType;

    @NotEmpty(message = "收款方品牌编号不能为空")
    private String toBrandSn;

    @NotEmpty(message = "收款方商户编号不能为空")
    private String toMerchantSn;

    private String toStoreSn;

    @Valid
    @NotNull(message = "收款方信息不能为空")
    private SettlementFlowAccountInfoVO toInfo;

    @Valid
    @NotNull(message = "金额信息不能为空")
    private SettlementFlowAmountVO amount;

    @Valid
    @NotNull(message = "交易信息不能为空")
    private SettlementFlowTradeInfoVO tradeInfo;

    @Valid
    @NotNull(message = "业务信息不能为空")
    private SettlementFlowBizInfoVO bizInfo;

    // 状态和批次
    @NotNull(message = "状态不能为空")
    private SettlementStatusEnum status;

    protected SettlementFlowAggrRoot() {
    }

    public static SettlementFlowAggrRoot newEmptyInstance() {
        return new SettlementFlowAggrRoot();
    }

    public void checkExist() {
        if (isNotExist()) {
            throw new FundSettlementBizException(FundSettlementRespCodeEnum.SETTLEMENT_FLOW_NOT_EXIST);
        }
    }

    public void updateStatus(SettlementStatusEnum status) {
        if (Objects.isNull(status)) {
            return;
        }
        this.status = status;
        modifyUpdated();
    }

    public void updateFromInfo(SettlementFlowAccountInfoVO fromInfo) {
        if (Objects.isNull(fromInfo)) {
            return;
        }
        this.fromInfo = fromInfo;
        modifyUpdated();
    }

    public void updateToInfo(SettlementFlowAccountInfoVO toInfo) {
        if (Objects.isNull(toInfo)) {
            return;
        }
        this.toInfo = toInfo;
        modifyUpdated();
    }

    public void updateAmount(SettlementFlowAmountVO amount) {
        if (Objects.isNull(amount)) {
            return;
        }
        this.amount = amount;
        modifyUpdated();
    }

    public void updateBizInfo(SettlementFlowBizInfoVO bizInfo) {
        if (Objects.isNull(bizInfo)) {
            return;
        }
        this.bizInfo = bizInfo;
        modifyUpdated();
    }

    public boolean isPending() {
        return Objects.equals(this.status, SettlementStatusEnum.PENDING);
    }

    public boolean isProcessing() {
        return Objects.equals(this.status, SettlementStatusEnum.PROCESSING);
    }

    public Integer getAcquiringCompany() {
        return tradeInfo.getAcquiringCompany();
    }

    public LocalDate getSettleTimeLocalDate() {
        return LocalDate.parse(tradeInfo.getSettleTime());
    }

    public boolean isSuccess() {
        return Objects.equals(this.status, SettlementStatusEnum.SUCCESS);
    }

    public boolean isFailed() {
        return Objects.equals(this.status, SettlementStatusEnum.FAILED);
    }

    @Override
    protected void setId(Long id) {
        super.id = id;
    }

    @Override
    protected void setCreated(LocalDateTime created) {
        super.created = created;
    }

    @Override
    protected void setUpdated(LocalDateTime updated) {
        super.updated = updated;
    }

    @Override
    protected void setVersion(Long version) {
        super.version = version;
    }
}
