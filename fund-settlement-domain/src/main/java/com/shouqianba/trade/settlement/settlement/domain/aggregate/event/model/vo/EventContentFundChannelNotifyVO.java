package com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotEmpty;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventContentFundChannelNotifyVO extends BaseVO<EventContentFundChannelNotifyVO> {

    @NotEmpty(message = "brand_id不能为空")
    private String brandId;

    @NotEmpty(message = "action_id不能为空")
    private String actionId;

    @NotEmpty(message = "settlement_channel不能为空")
    private Integer settlementChannel;

    @NotEmpty(message = "settlement_bank_account不能为空")
    private Long amount;

    @NotEmpty(message = "entry_time不能为空")
    private Long entryTime;

    @Override
    protected EventContentFundChannelNotifyVO doReplaceNotNull(EventContentFundChannelNotifyVO vo) {
        EventContentFundChannelNotifyVO.EventContentFundChannelNotifyVOBuilder builder = toBuilder();

        if (Objects.nonNull(vo.getBrandId())) {
            builder.brandId(vo.getBrandId());
        }

        if (Objects.nonNull(vo.getActionId())) {
            builder.actionId(vo.getActionId());
        }

        if (Objects.nonNull(vo.getSettlementChannel())) {
            builder.settlementChannel(vo.getSettlementChannel());
        }

        if (Objects.nonNull(vo.getAmount())) {
            builder.amount(vo.getAmount());
        }

        if (Objects.nonNull(vo.getEntryTime())) {
            builder.entryTime(vo.getEntryTime());
        }

        return builder.build();
    }

    public LocalDate genSettleTime() {
        if (entryTime == null) {
            return null;
        }
        return Instant
                .ofEpochMilli(entryTime)
                .atZone(ZoneId.systemDefault())  // 指定时区
                .toLocalDate();
    }
}