package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * 统计类型枚举
 *
 * <AUTHOR> Date: 2025/07/15 Time: 10:34 AM
 */
@Getter
public enum StatisticsTypeEnum {
    
    /**
     * 商户维度清分统计
     */
    MERCHANT_CLEARING((byte) 1, "商户维度清分统计"),

    /**
     * 商户维度分账统计
     */
    MERCHANT_SPLIT((byte) 2, "商户维度分账统计");

    private final byte code;
    private final String desc;

    StatisticsTypeEnum(byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public byte getCode() {
        return code;
    }

    @JsonCreator
    public static StatisticsTypeEnum ofCode(Byte code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (StatisticsTypeEnum typeEnum : StatisticsTypeEnum.values()) {
            if (typeEnum.getCode() == code) {
                return typeEnum;
            }
        }
        return null;
    }
}