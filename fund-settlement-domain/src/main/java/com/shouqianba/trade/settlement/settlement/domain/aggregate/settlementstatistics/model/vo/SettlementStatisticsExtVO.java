package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.vo;

import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

/**
 * 结算统计扩展信息VO
 *
 * <AUTHOR> Date: 2025/07/15 Time: 10:34 AM
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
public class SettlementStatisticsExtVO extends BaseVO<SettlementStatisticsExtVO> {


    public static SettlementStatisticsExtVO newEmptyInstance() {
        return SettlementStatisticsExtVO.builder().build();
    }

    @Override
    protected SettlementStatisticsExtVO doReplaceNotNull(SettlementStatisticsExtVO vo) {
        return null;
    }
}