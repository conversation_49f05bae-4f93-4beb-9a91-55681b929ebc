package com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/3/17 Time: 16:20 PM
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventContentBatchEntrySettlementVO extends BaseVO<EventContentBatchEntrySettlementVO> {

    private String adjustSn;

    @Override
    protected EventContentBatchEntrySettlementVO doReplaceNotNull(EventContentBatchEntrySettlementVO vo) {
        EventContentBatchEntrySettlementVO.EventContentBatchEntrySettlementVOBuilder builder = toBuilder();

        if (Objects.nonNull(vo.getAdjustSn())) {
            builder.adjustSn(vo.getAdjustSn());
        }

        return builder.build();
    }

}
