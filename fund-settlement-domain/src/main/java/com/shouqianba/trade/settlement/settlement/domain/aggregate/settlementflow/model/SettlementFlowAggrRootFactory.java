package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model;

import com.shouqianba.trade.fund.settlement.common.exception.FundSettlementBizException;
import com.shouqianba.trade.fund.settlement.common.exception.enums.FundSettlementRespCodeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseFactory;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.AccountTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.FlowTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.SettlementStatusEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowAccountInfoVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowAmountVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowBizInfoVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo.SettlementFlowTradeInfoVO;
import com.wosai.general.util.validation.ValidationUtils;

import java.time.LocalDateTime;
import java.util.Objects;

/***
 * <AUTHOR> Date: 2025/04/24 Time: 04:34 PM
 */
public class SettlementFlowAggrRootFactory extends BaseFactory {
    public static SettlementFlowAggrRootBuilder builder() {
        return new SettlementFlowAggrRootBuilder(SettlementFlowAggrRoot.newEmptyInstance());
    }

    public static class SettlementFlowAggrRootBuilder extends BaseBuilder<SettlementFlowAggrRoot, SettlementFlowAggrRootCoreBuilder, SettlementFlowAggrRootOptionalBuilder> {
        private SettlementFlowAggrRootCoreBuilder settlementFlowAggrRootCoreBuilder;
        private SettlementFlowAggrRootOptionalBuilder settlementFlowAggrRootOptionalBuilder;

        protected SettlementFlowAggrRootBuilder(SettlementFlowAggrRoot settlementFlowAggrRoot) {
            super(settlementFlowAggrRoot);
        }

        @Override
        public SettlementFlowAggrRootCoreBuilder coreBuilder() {
            if (Objects.isNull(settlementFlowAggrRootCoreBuilder)) {
                settlementFlowAggrRootCoreBuilder = new SettlementFlowAggrRootCoreBuilder(aggrRoot);
            }
            return settlementFlowAggrRootCoreBuilder;
        }

        @Override
        public SettlementFlowAggrRootOptionalBuilder optionalBuilder() {
            if (Objects.isNull(settlementFlowAggrRootOptionalBuilder)) {
                settlementFlowAggrRootOptionalBuilder = new SettlementFlowAggrRootOptionalBuilder(aggrRoot);
            }
            return settlementFlowAggrRootOptionalBuilder;
        }

        @Override
        protected void checkParams() {
            ValidationUtils.ValidationResult result = ValidationUtils.validate(aggrRoot);
            if (result.isInvalid()) {
                throw new FundSettlementBizException(FundSettlementRespCodeEnum.ILLEGAL_ARGUMENT
                        , result.getMsg());
            }
        }
    }

    public static class SettlementFlowAggrRootCoreBuilder extends SettlementFlowAggrRootBuilder implements BaseCoreBuilder {

        protected SettlementFlowAggrRootCoreBuilder(SettlementFlowAggrRoot settlementFlowAggrRoot) {
            super(settlementFlowAggrRoot);
        }

        public SettlementFlowAggrRootCoreBuilder id(Long id) {
            aggrRoot.setId(id);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder type(FlowTypeEnum flowType) {
            aggrRoot.setType(flowType);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder transSn(String transSn) {
            aggrRoot.setTransSn(transSn);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder orderSn(String orderSn) {
            aggrRoot.setOrderSn(orderSn);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder batchId(Long batchId) {
            aggrRoot.setBatchId(batchId);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder poolId(Long poolId) {
            aggrRoot.setPoolId(poolId);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder fromType(AccountTypeEnum fromType) {
            aggrRoot.setFromType(fromType);
            return this;
        }


        // 新增fromSn相关方法
        public SettlementFlowAggrRootCoreBuilder fromBrandSn(String fromBrandSn) {
            aggrRoot.setFromBrandSn(fromBrandSn);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder fromMerchantSn(String fromMerchantSn) {
            aggrRoot.setFromMerchantSn(fromMerchantSn);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder fromStoreSn(String fromStoreSn) {
            aggrRoot.setFromStoreSn(fromStoreSn);
            return this;
        }


        public SettlementFlowAggrRootCoreBuilder fromInfo(SettlementFlowAccountInfoVO fromInfo) {
            aggrRoot.setFromInfo(fromInfo);
            return this;
        }


        public SettlementFlowAggrRootCoreBuilder toType(AccountTypeEnum toType) {
            aggrRoot.setToType(toType);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder toBrandSn(String toBrandSn) {
            aggrRoot.setToBrandSn(toBrandSn);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder toMerchantSn(String toMerchantSn) {
            aggrRoot.setToMerchantSn(toMerchantSn);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder toStoreSn(String toStoreSn) {
            aggrRoot.setToStoreSn(toStoreSn);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder toInfo(SettlementFlowAccountInfoVO toInfo) {
            aggrRoot.setToInfo(toInfo);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder amount(SettlementFlowAmountVO amount) {
            aggrRoot.setAmount(amount);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder tradeInfo(SettlementFlowTradeInfoVO tradeInfoVO) {
            aggrRoot.setTradeInfo(tradeInfoVO);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder bizInfo(SettlementFlowBizInfoVO bizInfo) {
            aggrRoot.setBizInfo(bizInfo);
            return this;
        }

        public SettlementFlowAggrRootCoreBuilder status(SettlementStatusEnum status) {
            aggrRoot.setStatus(status);
            return this;
        }
    }

    public static class SettlementFlowAggrRootOptionalBuilder extends SettlementFlowAggrRootBuilder implements BaseOptionalBuilder {

        protected SettlementFlowAggrRootOptionalBuilder(SettlementFlowAggrRoot settlementFlowAggrRoot) {
            super(settlementFlowAggrRoot);
        }

        public SettlementFlowAggrRootOptionalBuilder created(LocalDateTime created) {
            aggrRoot.setCreated(created);
            return this;
        }

        public SettlementFlowAggrRootOptionalBuilder updated(LocalDateTime updated) {
            aggrRoot.setUpdated(updated);
            return this;
        }

        public SettlementFlowAggrRootOptionalBuilder version(Long version) {
            aggrRoot.setVersion(version);
            return this;
        }

        @Override
        public void initOptional() {
            LocalDateTime created = aggrRoot.getCreated();
            LocalDateTime updated = aggrRoot.getUpdated();
            Long version = aggrRoot.getVersion();

            LocalDateTime currentDateTime = LocalDateTime.now();
            if (Objects.isNull(created)) {
                aggrRoot.setCreated(currentDateTime);
            }
            if (Objects.isNull(updated)) {
                aggrRoot.setUpdated(currentDateTime);
            }
            if (Objects.isNull(version)) {
                aggrRoot.setVersion(0L);
            }
        }
    }
}
