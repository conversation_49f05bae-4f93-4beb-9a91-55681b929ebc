package com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

/**
 * <AUTHOR> Date: 2025/3/17 Time: 16:20 PM
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventContentAccountingSqbBillVO extends BaseVO<EventContentAccountingSqbBillVO> {

    @Override
    protected EventContentAccountingSqbBillVO doReplaceNotNull(EventContentAccountingSqbBillVO vo) {
        EventContentAccountingSqbBillVO.EventContentAccountingSqbBillVOBuilder builder = toBuilder();
        return builder.build();
    }

}
