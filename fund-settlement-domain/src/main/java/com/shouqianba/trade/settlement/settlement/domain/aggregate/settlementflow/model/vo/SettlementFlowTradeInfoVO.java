package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.fund.settlement.common.util.JsonUtils;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.enums.SettleTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.FlowChannelEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.PaywayEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/***
 * <AUTHOR> Date: 2025/04/24 Time: 04:34 PM
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementFlowTradeInfoVO extends BaseVO<SettlementFlowTradeInfoVO> {

    @NotNull(message = "流水渠道不能为空")
    private FlowChannelEnum flowChannel;

    @NotNull(message = "结算类型不能为空")
    private SettleTypeEnum settleType;

    @NotNull(message = "收单机构不能为空")
    private Integer acquiringCompany;

    @NotEmpty(message = "交易时间不能为空")
    private String tradeTime;

    @NotEmpty(message = "交易时间不能为空")
    private String settleTime;

    @NotNull(message = "支付方式不能为空")
    private PaywayEnum payWay;

    /**
     * 渠道交易流水号
     */
    private String channelTransSn;

    /**
     * 渠道订单号
     */
    private String channelOrderSn;

    /**
     * 银行流水号
     */
    private String bankTransSn;


    /**
     * 转换为JSON字符串
     *
     * @return JSON字符串
     */
    public String toJsonString() {
        return JsonUtils.toJsonString(this);
    }

    /**
     * 创建空实例
     *
     * @return 空实例
     */
    public static SettlementFlowTradeInfoVO newEmptyInstance() {
        return SettlementFlowTradeInfoVO
                .builder()
                .build();
    }

    @Override
    protected SettlementFlowTradeInfoVO doReplaceNotNull(SettlementFlowTradeInfoVO vo) {
        SettlementFlowTradeInfoVO.SettlementFlowTradeInfoVOBuilder builder = toBuilder();

        if (vo.getPayWay() != null) {
            builder.payWay(vo.getPayWay());
        }
        if (vo.getTradeTime() != null) {
            builder.tradeTime(vo.getTradeTime());
        }
        if (vo.getChannelOrderSn() != null) {
            builder.channelOrderSn(vo.getChannelOrderSn());
        }
        if (vo.getBankTransSn() != null) {
            builder.bankTransSn(vo.getBankTransSn());
        }
        if (vo.getChannelTransSn() != null) {
            builder.channelTransSn(vo.getChannelTransSn());
        }
        if (vo.getAcquiringCompany() != null) {
            builder.acquiringCompany(vo.getAcquiringCompany());
        }
        if (vo.getFlowChannel() != null) {
            builder.flowChannel(vo.getFlowChannel());
        }
        if (vo.getSettleType() != null) {
            builder.settleType(vo.getSettleType());
        }

        return builder.build();
    }
}
