package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/***
 * <AUTHOR> Date: 2025/5/19 Time: 4:34 PM
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
public class SettlementBatchAccountInfoVO extends BaseVO<SettlementBatchAccountInfoVO> {

    @NotEmpty(message = "品牌编号不能为空")
    @Size(max = 64, message = "品牌编号格式错误")
    private String brandSn;

    @NotEmpty(message = "品牌ID不能为空")
    @Size(max = 64, message = "品牌ID格式错误")
    private String brandId;

    @NotEmpty(message = "收付通商户编号不能为空")
    @Size(max = 64, message = "收付通商户编号格式错误")
    private String fundMerchantSn;

    @NotEmpty(message = "收付通商户ID不能为空")
    @Size(max = 64, message = "收付通商户ID格式错误")
    private String fundMerchantId;

    @Size(max = 64, message = "收钱吧商户编号格式错误")
    private String sqbMerchantSn;

    @Size(max = 64, message = "收钱吧商户ID格式错误")
    private String sqbMerchantId;

    @Size(max = 64, message = "店铺编号格式错误")
    private String storeSn;

    @Size(max = 64, message = "店铺ID格式错误")
    private String storeId;

    @Size(max = 100, message = "渠道商户编号格式错误")
    private String channelMerchantSn;


    public static SettlementBatchAccountInfoVO newEmptyInstance() {
        return SettlementBatchAccountInfoVO.builder().build();
    }

    @Override
    protected SettlementBatchAccountInfoVO doReplaceNotNull(SettlementBatchAccountInfoVO vo) {
        SettlementBatchAccountInfoVOBuilder builder = toBuilder();

        if (vo.getBrandId() != null) {
            builder.brandId(vo.getBrandId());
        }
        if (vo.getBrandSn() != null) {
            builder.brandSn(vo.getBrandSn());
        }
        if (vo.getFundMerchantId() != null) {
            builder.fundMerchantId(vo.getFundMerchantId());
        }
        if (vo.getFundMerchantSn() != null) {
            builder.fundMerchantSn(vo.getFundMerchantSn());
        }
        if (vo.getSqbMerchantId() != null) {
            builder.sqbMerchantId(vo.getSqbMerchantId());
        }
        if (vo.getSqbMerchantSn() != null) {
            builder.sqbMerchantSn(vo.getSqbMerchantSn());
        }
        if (vo.getStoreId() != null) {
            builder.storeId(vo.getStoreId());
        }
        if (vo.getStoreSn() != null) {
            builder.storeSn(vo.getStoreSn());
        }
        if (vo.getChannelMerchantSn() != null) {
            builder.channelMerchantSn(vo.getChannelMerchantSn());
        }

        return builder.build();
    }
}
