package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.repository;

import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.SettlementStatisticsAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.query.SettlementStatisticsAggrQuery;

import java.util.List;

/**
 * 结算统计仓储接口
 *
 * <AUTHOR> Date: 2025/07/15 Time: 10:34 AM
 */
public interface SettlementStatisticsDomainRepository {

    /**
     * 保存结算统计
     *
     * @param settlementStatisticsAggrRoot 结算统计聚合根
     */
    void save(SettlementStatisticsAggrRoot settlementStatisticsAggrRoot);

    /**
     * 查询结算统计
     *
     * @param query 查询条件
     * @return 结算统计聚合根
     */
    SettlementStatisticsAggrRoot query(SettlementStatisticsAggrQuery query);

    /**
     * 批量查询结算统计
     *
     * @param query 查询条件
     * @return 结算统计聚合根列表
     */
    List<SettlementStatisticsAggrRoot> batchQuery(SettlementStatisticsAggrQuery query);

}